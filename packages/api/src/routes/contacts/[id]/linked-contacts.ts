import { <PERSON>o } from "hono";
import { z<PERSON>alida<PERSON> } from "@hono/zod-validator";
import { z } from "zod";
import { authMiddleware } from "../../../middleware/auth";
import { verifyOrganizationMembership } from "../../organizations/lib/membership";
import { logger } from "@repo/logs";
import { db } from "@repo/database/server";
import type { Session } from "@repo/auth";
import { ObjectId } from "mongodb";

export const app = new Hono<{
  Variables: { user: Session["user"] };
}>();

// Validation schemas
const linkContactSchema = z.object({
  contactId: z.string(), // ID of the contact to link
  object1Role: z.string().min(1, "Object1 role is required"), // Relation from current contact's perspective
  object2Role: z.string().min(1, "Object2 role is required"), // Relation from linked contact's perspective
});

const updateRelationSchema = z.object({
  relationshipId: z.string(), // ID of the ObjectRelationship record
  object1Role: z.string().min(1, "Object1 role is required"),
  object2Role: z.string().min(1, "Object2 role is required"),
});

const unlinkContactSchema = z.object({
  relationshipId: z.string(), // ID of the ObjectRelationship record to delete
});

// GET /:id/linked-contacts - Get all linked contacts for a contact
app.get(
  "/",
  authMiddleware,
  async (c) => {
    try {
      const user = c.get("user");
      const contactId = c.req.param("id");
      const organizationId = c.req.query("organizationId");

      if (!organizationId) {
        return c.json({ error: "Organization ID is required" }, 400);
      }

      // Verify organization membership
      await verifyOrganizationMembership(organizationId, user.id);

      logger.info("Fetching linked contacts", { contactId, organizationId });

      // Find all ObjectRelationship records where this contact is either object1 or object2
      // and both objects are contacts
      const relationships = await db.objectRelationship.findMany({
        where: {
          organizationId,
          isActive: true,
          OR: [
            {
              object1Id: contactId,
              object1Type: "contact",
              object2Type: "contact"
            },
            {
              object2Id: contactId,
              object1Type: "contact", 
              object2Type: "contact"
            }
          ]
        },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          }
        }
      });

      // Fetch the linked contact details for each relationship
      const linkedContactsData = await Promise.all(
        relationships.map(async (relationship) => {
          const isCurrentContactObject1 = relationship.object1Id === contactId;
          const linkedContactId = isCurrentContactObject1 ? relationship.object2Id : relationship.object1Id;
          
          // Fetch the linked contact details
          const linkedContact = await db.contact.findFirst({
            where: {
              id: linkedContactId,
              organizationId,
              isDeleted: false,
            },
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              phone: true,
              image: true,
            }
          });

          if (!linkedContact) {
            logger.warn("Linked contact not found", { linkedContactId, relationshipId: relationship.id });
            return null;
          }

          return {
            id: relationship.id,
            contact1Id: relationship.object1Id,
            contact1Relation: relationship.object1Role,
            contact2Id: relationship.object2Id,
            contact2Relation: relationship.object2Role,
            contact1: isCurrentContactObject1 ? null : linkedContact,
            contact2: isCurrentContactObject1 ? linkedContact : null,
            createdAt: relationship.createdAt,
            updatedAt: relationship.updatedAt,
          };
        })
      );

      // Filter out null results
      const validLinkedContacts = linkedContactsData.filter(Boolean);

      return c.json({
        data: validLinkedContacts,
        success: true,
      });
    } catch (error) {
      logger.error("Error fetching linked contacts", {
        error: error instanceof Error ? error.message : String(error),
        contactId: c.req.param("id"),
        organizationId: c.req.query("organizationId"),
      });

      return c.json(
        {
          error: "Failed to fetch linked contacts",
          success: false,
        },
        500
      );
    }
  }
);

// POST /:id/linked-contacts - Link two contacts
app.post(
  "/",
  authMiddleware,
  zValidator("json", linkContactSchema),
  async (c) => {
    try {
      const user = c.get("user");
      const currentContactId = c.req.param("id");
      const { contactId: targetContactId, object1Role, object2Role } = c.req.valid("json");
      const organizationId = c.req.query("organizationId");

      if (!currentContactId) {
        return c.json({ error: "Contact ID is required" }, 400);
      }

      if (!organizationId) {
        return c.json({ error: "Organization ID is required" }, 400);
      }

      // Verify organization membership
      await verifyOrganizationMembership(organizationId, user.id);

      logger.info("Linking contacts", {
        currentContactId,
        targetContactId,
        object1Role,
        object2Role,
        organizationId,
        userId: user.id,
      });

      // Validate that both contacts exist and belong to the organization
      const [currentContact, targetContact] = await Promise.all([
        db.contact.findFirst({
          where: {
            id: currentContactId,
            organizationId,
            isDeleted: false,
          },
        }),
        db.contact.findFirst({
          where: {
            id: targetContactId,
            organizationId,
            isDeleted: false,
          },
        }),
      ]);

      if (!currentContact) {
        return c.json(
          {
            error: "Current contact not found",
            success: false,
          },
          404
        );
      }

      if (!targetContact) {
        return c.json(
          {
            error: "Target contact not found",
            success: false,
          },
          404
        );
      }

      // Prevent linking a contact to itself
      if (currentContactId === targetContactId) {
        return c.json(
          {
            error: "Cannot link a contact to itself",
            success: false,
          },
          400
        );
      }

      // Check if the contacts are already linked
      const existingRelationship = await db.objectRelationship.findFirst({
        where: {
          organizationId,
          isActive: true,
          OR: [
            {
              object1Id: currentContactId,
              object1Type: "contact",
              object2Id: targetContactId,
              object2Type: "contact",
            },
            {
              object1Id: targetContactId,
              object1Type: "contact",
              object2Id: currentContactId,
              object2Type: "contact",
            },
          ],
        },
      });

      if (existingRelationship) {
        return c.json(
          {
            error: "Contacts are already linked",
            success: false,
          },
          400
        );
      }

      // Create ObjectRelationship record with bidirectional relationship
      const relationship = await db.objectRelationship.create({
        data: {
          id: new ObjectId().toHexString(),
          object1Id: currentContactId,
          object1Type: "contact",
          object1Role,
          object2Id: targetContactId,
          object2Type: "contact",
          object2Role,
          organizationId,
          isActive: true,
          createdBy: user.id,
        },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          }
        }
      });

      // Fetch the contact details to return in response
      const [contact1Details, contact2Details] = await Promise.all([
        db.contact.findFirst({
          where: { id: currentContactId },
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
            image: true,
          }
        }),
        db.contact.findFirst({
          where: { id: targetContactId },
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
            image: true,
          }
        })
      ]);

      const responseData = {
        id: relationship.id,
        contact1Id: relationship.object1Id,
        contact1Relation: relationship.object1Role,
        contact2Id: relationship.object2Id,
        contact2Relation: relationship.object2Role,
        contact1: contact1Details,
        contact2: contact2Details,
        createdAt: relationship.createdAt,
        updatedAt: relationship.updatedAt,
      };

      return c.json({
        data: responseData,
        success: true,
      });
    } catch (error) {
      logger.error("Error linking contacts", {
        error: error instanceof Error ? error.message : String(error),
        currentContactId: c.req.param("id"),
        organizationId: c.req.query("organizationId"),
      });

      return c.json(
        {
          error: "Failed to link contacts",
          success: false,
        },
        500
      );
    }
  }
);

// PUT /:id/linked-contacts - Update relationship labels
app.put(
  "/",
  authMiddleware,
  zValidator("json", updateRelationSchema),
  async (c) => {
    try {
      const user = c.get("user");
      const contactId = c.req.param("id");
      const { relationshipId, object1Role, object2Role } = c.req.valid("json");
      const organizationId = c.req.query("organizationId");

      if (!organizationId) {
        return c.json({ error: "Organization ID is required" }, 400);
      }

      // Verify organization membership
      await verifyOrganizationMembership(organizationId, user.id);

      logger.info("Updating contact relationship", {
        contactId,
        relationshipId,
        object1Role,
        object2Role,
        organizationId,
        userId: user.id,
      });

      // Find and update the ObjectRelationship record
      const existingRelationship = await db.objectRelationship.findFirst({
        where: {
          id: relationshipId,
          organizationId,
          isActive: true,
        },
      });

      if (!existingRelationship) {
        return c.json(
          {
            error: "Relationship record not found",
            success: false,
          },
          404
        );
      }

      // Update the ObjectRelationship record with new relation labels
      const updatedRelationship = await db.objectRelationship.update({
        where: {
          id: relationshipId,
        },
        data: {
          object1Role,
          object2Role,
          updatedBy: user.id,
        },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          }
        }
      });

      // Fetch the contact details to return in response
      const [contact1Details, contact2Details] = await Promise.all([
        db.contact.findFirst({
          where: { id: updatedRelationship.object1Id },
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
            image: true,
          }
        }),
        db.contact.findFirst({
          where: { id: updatedRelationship.object2Id },
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
            image: true,
          }
        })
      ]);

      const responseData = {
        id: updatedRelationship.id,
        contact1Id: updatedRelationship.object1Id,
        contact1Relation: updatedRelationship.object1Role,
        contact2Id: updatedRelationship.object2Id,
        contact2Relation: updatedRelationship.object2Role,
        contact1: contact1Details,
        contact2: contact2Details,
        createdAt: updatedRelationship.createdAt,
        updatedAt: updatedRelationship.updatedAt,
      };

      return c.json({
        data: responseData,
        success: true,
      });
    } catch (error) {
      logger.error("Error updating contact relationship", {
        error: error instanceof Error ? error.message : String(error),
        contactId: c.req.param("id"),
        organizationId: c.req.query("organizationId"),
      });

      return c.json(
        {
          error: "Failed to update contact relationship",
          success: false,
        },
        500
      );
    }
  }
);

// DELETE /:id/linked-contacts - Unlink contacts
app.delete(
  "/",
  authMiddleware,
  zValidator("json", unlinkContactSchema),
  async (c) => {
    try {
      const user = c.get("user");
      const contactId = c.req.param("id");
      const { relationshipId } = c.req.valid("json");
      const organizationId = c.req.query("organizationId");

      if (!organizationId) {
        return c.json({ error: "Organization ID is required" }, 400);
      }

      // Verify organization membership
      await verifyOrganizationMembership(organizationId, user.id);

      logger.info("Unlinking contacts", {
        contactId,
        relationshipId,
        organizationId,
        userId: user.id,
      });

      // Find and soft delete the ObjectRelationship record
      const existingRelationship = await db.objectRelationship.findFirst({
        where: {
          id: relationshipId,
          organizationId,
          isActive: true,
        },
      });

      if (!existingRelationship) {
        return c.json(
          {
            error: "Relationship record not found",
            success: false,
          },
          404
        );
      }

      // Soft delete by setting isActive to false
      await db.objectRelationship.update({
        where: {
          id: relationshipId,
        },
        data: {
          isActive: false,
          updatedBy: user.id,
        },
      });

      return c.json({
        success: true,
        message: "Contacts unlinked successfully",
      });
    } catch (error) {
      logger.error("Error unlinking contacts", {
        error: error instanceof Error ? error.message : String(error),
        contactId: c.req.param("id"),
        organizationId: c.req.query("organizationId"),
      });

      return c.json(
        {
          error: "Failed to unlink contacts",
          success: false,
        },
        500
      );
    }
  }
);

export default app; 