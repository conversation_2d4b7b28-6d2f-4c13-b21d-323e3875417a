import { Hono } from "hono";
import { authMiddleware } from "../../middleware/auth";
import { db } from "@repo/database/server";
import { EmailActivitySchema, EmailSourceTypeSchema, EmailDirectionSchema } from "@repo/database/src/zod";
import { z } from "zod";
import { ObjectId } from "mongodb";

export const emailActivitiesRouter = new Hono();

// Create email activity
const createEmailActivitySchema = EmailActivitySchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  organizationId: z.string(),
});

emailActivitiesRouter.post("/", authMiddleware, async (c) => {
  const user = c.get("user");
  const body = await c.req.json();

  try {
    const validatedData = createEmailActivitySchema.parse(body);

    // Verify user has access to the organization
    const membership = await db.member.findFirst({
      where: {
        userId: user.id,
        organizationId: validatedData.organizationId,
      },
    });

    if (!membership) {
      return c.json({ error: "Access denied to organization" }, 403);
    }

    const emailActivity = await db.emailActivity.create({
      data: {
        ...validatedData,
        id: new ObjectId().toHexString(),
      },
    });

    return c.json(emailActivity, 201);
  } catch (error) {
    console.error("Error creating email activity:", error);
    if (error instanceof z.ZodError) {
      return c.json({ error: "Invalid data", details: error.errors }, 400);
    }
    return c.json({ error: "Failed to create email activity" }, 500);
  }
});

// Get email activities with filtering and pagination
emailActivitiesRouter.get("/", authMiddleware, async (c) => {
  const user = c.get("user");
  const organizationId = c.req.query("organizationId");
  const sourceType = c.req.query("sourceType");
  const direction = c.req.query("direction");
  const linkedContactId = c.req.query("linkedContactId");
  const linkedCompanyId = c.req.query("linkedCompanyId");
  const threadId = c.req.query("threadId");
  const fromEmail = c.req.query("from");
  const isRead = c.req.query("isRead");
  const page = parseInt(c.req.query("page") || "1");
  const limit = Math.min(parseInt(c.req.query("limit") || "20"), 100);
  const skip = (page - 1) * limit;

  if (!organizationId) {
    return c.json({ error: "organizationId is required" }, 400);
  }

  try {
    // Verify user has access to the organization
    const membership = await db.member.findFirst({
      where: {
        userId: user.id,
        organizationId,
      },
    });

    if (!membership) {
      return c.json({ error: "Access denied to organization" }, 403);
    }

    // Build filter conditions
    const where: any = {
      organizationId,
    };

    if (sourceType && EmailSourceTypeSchema.safeParse(sourceType).success) {
      where.sourceType = sourceType;
    }

    if (direction && EmailDirectionSchema.safeParse(direction).success) {
      where.direction = direction;
    }

    if (linkedContactId) {
      where.linkedContacts = {
        has: linkedContactId,
      };
    }

    if (linkedCompanyId) {
      where.linkedCompanies = {
        has: linkedCompanyId,
      };
    }

    if (threadId) {
      where.threadId = threadId;
    }

    if (fromEmail) {
      where.from = {
        contains: fromEmail,
        mode: "insensitive",
      };
    }

    if (isRead !== undefined) {
      where.isRead = isRead === "true";
    }

    // Get activities with user information
    const [activities, totalCount] = await Promise.all([
      db.emailActivity.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
        },
        orderBy: {
          timestamp: "desc",
        },
        skip,
        take: limit,
      }),
      db.emailActivity.count({ where }),
    ]);

    return c.json({
      activities,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching email activities:", error);
    return c.json({ error: "Failed to fetch email activities" }, 500);
  }
});

// Get single email activity
emailActivitiesRouter.get("/:id", authMiddleware, async (c) => {
  const user = c.get("user");
  const activityId = c.req.param("id");

  try {
    const activity = await db.emailActivity.findFirst({
      where: {
        id: activityId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });

    if (!activity) {
      return c.json({ error: "Email activity not found" }, 404);
    }

    // Verify user has access to the organization
    const membership = await db.member.findFirst({
      where: {
        userId: user.id,
        organizationId: activity.organizationId,
      },
    });

    if (!membership) {
      return c.json({ error: "Access denied" }, 403);
    }

    return c.json(activity);
  } catch (error) {
    console.error("Error fetching email activity:", error);
    return c.json({ error: "Failed to fetch email activity" }, 500);
  }
});

// Update email activity (for marking as read, linking to records, etc.)
const updateEmailActivitySchema = z.object({
  isRead: z.boolean().optional(),
  isImportant: z.boolean().optional(),
  linkedContacts: z.string().array().optional(),
  linkedCompanies: z.string().array().optional(),
  linkedDeals: z.string().array().optional(),
  linkedTasks: z.string().array().optional(),
  processingNotes: z.string().optional(),
});

emailActivitiesRouter.patch("/:id", authMiddleware, async (c) => {
  const user = c.get("user");
  const activityId = c.req.param("id");
  const body = await c.req.json();

  try {
    const validatedData = updateEmailActivitySchema.parse(body);

    // Check if activity exists and user has access
    const existingActivity = await db.emailActivity.findFirst({
      where: {
        id: activityId,
      },
    });

    if (!existingActivity) {
      return c.json({ error: "Email activity not found" }, 404);
    }

    // Verify user has access to the organization
    const membership = await db.member.findFirst({
      where: {
        userId: user.id,
        organizationId: existingActivity.organizationId,
      },
    });

    if (!membership) {
      return c.json({ error: "Access denied" }, 403);
    }

    const updatedActivity = await db.emailActivity.update({
      where: {
        id: activityId,
      },
      data: validatedData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    return c.json(updatedActivity);
  } catch (error) {
    console.error("Error updating email activity:", error);
    if (error instanceof z.ZodError) {
      return c.json({ error: "Invalid data", details: error.errors }, 400);
    }
    return c.json({ error: "Failed to update email activity" }, 500);
  }
});

// Get email thread (grouped by threadId)
emailActivitiesRouter.get("/thread/:threadId", authMiddleware, async (c) => {
  const user = c.get("user");
  const threadId = c.req.param("threadId");
  const organizationId = c.req.query("organizationId");

  if (!organizationId) {
    return c.json({ error: "organizationId is required" }, 400);
  }

  try {
    // Verify user has access to the organization
    const membership = await db.member.findFirst({
      where: {
        userId: user.id,
        organizationId,
      },
    });

    if (!membership) {
      return c.json({ error: "Access denied to organization" }, 403);
    }

    const threadActivities = await db.emailActivity.findMany({
      where: {
        threadId,
        organizationId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
      orderBy: {
        timestamp: "asc",
      },
    });

    return c.json({ threadId, activities: threadActivities });
  } catch (error) {
    console.error("Error fetching email thread:", error);
    return c.json({ error: "Failed to fetch email thread" }, 500);
  }
});

// Delete email activity (soft delete by marking as processed)
emailActivitiesRouter.delete("/:id", authMiddleware, async (c) => {
  const user = c.get("user");
  const activityId = c.req.param("id");

  try {
    // Check if activity exists and user has access
    const existingActivity = await db.emailActivity.findFirst({
      where: {
        id: activityId,
      },
    });

    if (!existingActivity) {
      return c.json({ error: "Email activity not found" }, 404);
    }

    // Verify user has access to the organization
    const membership = await db.member.findFirst({
      where: {
        userId: user.id,
        organizationId: existingActivity.organizationId,
      },
    });

    if (!membership) {
      return c.json({ error: "Access denied" }, 403);
    }

    // Instead of hard delete, we could mark as archived/deleted
    await db.emailActivity.delete({
      where: {
        id: activityId,
      },
    });

    return c.json({ message: "Email activity deleted successfully" });
  } catch (error) {
    console.error("Error deleting email activity:", error);
    return c.json({ error: "Failed to delete email activity" }, 500);
  }
});