datasource db {
  provider  = "mongodb"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

generator zod {
  provider         = "zod-prisma-types"
  output           = "../src/zod"
  createInputTypes = false
  addIncludeType   = false
  addSelectType    = false
}

generator json {
  provider = "prisma-json-types-generator"
}

model CreditPurchase {
  id            String   @id @default(cuid()) @map("_id")
  userId        String?  @db.ObjectId
  organizationId String? @db.ObjectId
  purchaseId    String?  @db.ObjectId // Links to Purchase.id
  packageId     String   // Package identifier (starter, professional, enterprise)
  credits       Int      // Number of credits purchased
  price         Float    // Price paid (changed from Decimal to Float for MongoDB)
  stripePaymentIntentId String? // Stripe payment intent ID
  status        String   @default("pending") // pending, completed, failed, refunded
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  user         User?         @relation(fields: [userId], references: [id], onDelete: Cascade)
  organization Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  purchase     Purchase?     @relation(fields: [purchaseId], references: [id], onDelete: Cascade)

  @@map("credit_purchases")
}

model AiUsage {
  id             String   @id @default(cuid()) @map("_id")
  userId         String   @db.ObjectId
  organizationId String?  @db.ObjectId
  chatId         String?  @db.ObjectId
  feature        String   // "data_chat", "task_creation", "analytics", "general_chat"
  activity       String?  // Specific activity description
  creditsUsed    Int
  promptTokens   Int?
  completionTokens Int?
  toolCalls      Int?     @default(0)
  model          String?  @default("gpt-4")
  success        Boolean  @default(true)
  error          String?
  metadata       Json?    // Additional context/data
  createdAt      DateTime @default(now())

  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  organization Organization? @relation(fields: [organizationId], references: [id], onDelete: SetNull)
  chat         Chat?         @relation(fields: [chatId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([organizationId])
  @@index([organizationId, createdAt])
  @@index([organizationId, feature])
  @@index([userId, createdAt])
  @@map("ai_usage")
}

enum ChatStatus {
  ready
  submitted  
  streaming
}

enum Tool {
  searchWeb
  searchContacts
  searchProperties
  searchCompanies
  getTaskSummary
  getAnalytics
  getRecentActivity
  createTask
}

model Chat {
  id                    String        @id @map("_id") @db.ObjectId
  title                 String
  userId                String        @db.ObjectId
  user                  User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  organizationId        String?       @db.ObjectId // CRM chats can be organization-scoped
  organization          Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  isPublic              Boolean
  status                ChatStatus
  lastMessageTimestamp  DateTime
  branchId              String?       @db.ObjectId
  branch                Chat?         @relation("ChatBranch", fields: [branchId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  branches              Chat[]        @relation("ChatBranch")
  messages              Message[]
  aiUsage               AiUsage[]     // Link to AI usage tracking
  createdAt             DateTime      @default(now())
  updatedAt             DateTime      @updatedAt

  @@index([userId])
  @@index([organizationId])
  @@index([userId, lastMessageTimestamp])
  @@index([organizationId, userId])
  @@index([organizationId, createdAt])
  @@map("chat")
}

model Message {
  id                String   @id @map("_id") @db.ObjectId
  prompt            String
  userId            String   @db.ObjectId
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  chatId            String   @db.ObjectId
  chat              Chat     @relation(fields: [chatId], references: [id], onDelete: Cascade)
  modelId           String   @db.ObjectId
  model             Model    @relation(fields: [modelId], references: [id])
  
  // Message content and metadata
  uiMessages        String?  // JSON string of UI messages for streaming
  responseStreamId  String   // Stream ID for real-time streaming
  tool              Tool?    // CRM tool used (searchContacts, searchWeb, etc.)
  error             Json?    // Error information if generation failed
  content           String?  // Final generated content
  searchContent     String?  // Searchable content for indexing
  
  // Token usage and costs
  promptTokens      Int?
  completionTokens  Int?
  totalTokens       Int?
  creditsSpent      Float?
  
  // CRM audit fields (following organization pattern)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@index([chatId])
  @@index([responseStreamId])
  @@index([searchContent])
  @@index([userId])
  @@index([userId, createdAt])
  @@index([tool])
  @@map("message")
}

model Company {
  id               String   @id @map("_id") @db.ObjectId
  name             String
  website          String?
  industry         String?
  size             String?
  description      String?
  logo             String?
  
  // Contact Information
  address          Json?
  phone            String?
  email            String?
  
  // Relationships
  contacts         Contact[]
  objectTags       ObjectTag[]
  
  // Organization
  organizationId   String   @db.ObjectId
  organization     Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  // Audit fields
  createdBy        String   @db.ObjectId
  creator          User     @relation("CompanyCreator", fields: [createdBy], references: [id])
  updatedBy        String?  @db.ObjectId
  updater          User?    @relation("CompanyUpdater", fields: [updatedBy], references: [id])
  
  // Soft delete
  isDeleted        Boolean  @default(false)
  deletedAt        DateTime?
  deletedBy        String?  @db.ObjectId
  deleter          User?    @relation("CompanyDeleter", fields: [deletedBy], references: [id])
  
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  @@index([organizationId])
  @@index([organizationId, name])
  @@index([organizationId, isDeleted])
  @@map("company")
}

model Contact {
  id               String   @id @map("_id") @db.ObjectId
  apolloId         String?
  
  // Personal Information
  firstName        String?
  lastName         String?
  image            String?
  title            String?
  persona          String?
  status           String?
  
  // Contact Information
  address          Json?    // Array of address objects
  phone            Json?    // Array of phone objects
  email            Json?     // Array of email objects
  website          String?
  social           Json?    // Social media links object
  
  // Additional Info
  source           String?
  stage            String?
  birthday         String?
  age              Float?
  spouseName       String?
  summary          String?
  
  // Company Association
  companyId        String?  @db.ObjectId
  company          Company? @relation(fields: [companyId], references: [id])
  
  // Buyer Information
  buyerNeeds       Json?
  
  generatedSummary String?
  
  // Organization
  organizationId   String   @db.ObjectId
  organization     Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  // Relations
  relatedContacts  RelatedContact[] @relation("ContactRelatedContacts")
  linkedProperties LinkedProperty[]
  objectTags       ObjectTag[]
  linkedAsContact1 LinkedContact[] @relation("LinkedContactAsContact1")
  linkedAsContact2 LinkedContact[] @relation("LinkedContactAsContact2")
  
  // Audit fields
  createdBy        String   @db.ObjectId
  creator          User     @relation("ContactCreator", fields: [createdBy], references: [id])
  updatedBy        String?  @db.ObjectId
  updater          User?    @relation("ContactUpdater", fields: [updatedBy], references: [id])
  lastViewedAt     DateTime?
  lastViewedBy     String?  @db.ObjectId
  lastViewer       User?    @relation("ContactViewer", fields: [lastViewedBy], references: [id])
  
  // Soft delete
  isDeleted        Boolean  @default(false)
  deletedAt        DateTime?
  deletedBy        String?  @db.ObjectId
  deleter          User?    @relation("ContactDeleter", fields: [deletedBy], references: [id])
  
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  @@index([organizationId])
  @@index([organizationId, firstName, lastName])
  @@index([organizationId, email])
  @@index([organizationId, isDeleted])
  @@index([organizationId, status])
  @@index([organizationId, stage])
  @@map("contact")
}

model RelatedContact {
  id             String   @id @map("_id") @db.ObjectId
  
  // Personal Information
  firstName      String?
  lastName       String?
  label          String?
  
  // Contact Information
  address        Json?    // Array of address objects
  phone          Json?    // Array of phone objects
  email          Json?    // Array of email objects
  
  // Relations
  contactId      String   @db.ObjectId
  contact        Contact  @relation("ContactRelatedContacts", fields: [contactId], references: [id], onDelete: Cascade)
  
  // Organization
  organizationId String   @db.ObjectId
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  // Audit fields
  createdBy      String   @db.ObjectId
  creator        User     @relation("RelatedContactCreator", fields: [createdBy], references: [id])
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([contactId])
  @@index([organizationId])
  @@map("related_contact")
}

model CustomFieldDefinition {
  id             String   @id @map("_id") @db.ObjectId
  name           String
  label          String
  icon           String?
  type           String   // 'string', 'number', 'boolean', 'date', 'array', 'object', 'email', 'phone', 'select', 'multiselect'
  objectType     String   // 'contact', 'company', 'property', etc.
  organizationId String   @db.ObjectId
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  isRequired     Boolean  @default(false)
  isSystem       Boolean  @default(false) // System fields cannot be deleted
  options        Json?    // For select/dropdown options with colors: { values: [{ value: "active", label: "Active", color: "#22c55e" }] }
  position       Int      @default(0)
  isActive       Boolean  @default(true)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@unique([organizationId, objectType, name])
  @@index([organizationId, objectType])
  @@map("custom_field_definition")
}

model CustomObject {
  id               String   @id @map("_id") @db.ObjectId
  objectType       String   // 'contact', 'company', 'property', etc.
  organizationId   String   @db.ObjectId
  organization     Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdBy        String   @db.ObjectId
  creator          User     @relation("CustomObjectCreator", fields: [createdBy], references: [id])
  
  // Core system fields
  title            String?
  description      String?
  status           String?
  tags             String[] // Array of tag names or IDs
  
  // Custom fields stored as JSON
  customFields     Json?    // Stores all custom field values
  
  // Metadata
  lastViewedAt     DateTime?
  lastViewedBy     String?  @db.ObjectId
  lastViewer       User?    @relation("CustomObjectViewer", fields: [lastViewedBy], references: [id])
  
  // Soft delete
  isDeleted        Boolean  @default(false)
  deletedAt        DateTime?
  deletedBy        String?  @db.ObjectId
  deleter          User?    @relation("CustomObjectDeleter", fields: [deletedBy], references: [id])
  
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  @@index([organizationId, objectType])
  @@index([organizationId, objectType, isDeleted])
  @@index([organizationId, objectType, createdBy])
  @@index([organizationId, objectType, status])
  @@map("custom_object")
}

model Favorite {
  id             String   @id @map("_id") @db.ObjectId
  objectId       String   @db.ObjectId
  objectType     String
  userId         String   @db.ObjectId
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  organizationId String   @db.ObjectId
  folderId       String?  @db.ObjectId
  position       Float?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([organizationId, userId], name: "by_organization_user")
  @@index([objectId], name: "by_object")
  @@map("favorite")
}

model FavoriteFolder {
  id             String   @id @map("_id") @db.ObjectId
  name           String
  organizationId String   @db.ObjectId
  userId         String   @db.ObjectId
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  isOpen         Boolean?
  position       Float?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([organizationId, userId], name: "by_organization_user")
  @@map("favorite_folder")
}

model Feedback {
  id        String   @id @map("_id") @db.ObjectId
  userId    String?  @db.ObjectId
  user      User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  feedback  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("feedback")
}

model List {
  id             String   @id @map("_id") @db.ObjectId
  name           String
  description    String?
  objectType     String   // What type of objects this list contains
  filters        Json?    // Criteria for dynamic lists
  isStatic       Boolean  @default(true) // Static vs dynamic lists
  organizationId String   @db.ObjectId
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdBy      String   @db.ObjectId
  creator        User     @relation("ListCreator", fields: [createdBy], references: [id])
  
  // Relations
  permissions    ListPermission[]
  items          ListItem[]
  
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([organizationId, objectType])
  @@map("list")
}

model ListPermission {
  id     String   @id @map("_id") @db.ObjectId
  listId String   @db.ObjectId
  list   List     @relation(fields: [listId], references: [id], onDelete: Cascade)
  userId String   @db.ObjectId
  user   User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  role   ListRole
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([listId, userId])
  @@index([userId])
  @@map("list_permission")
}

model ListItem {
  id         String @id @map("_id") @db.ObjectId
  listId     String @db.ObjectId
  list       List   @relation(fields: [listId], references: [id], onDelete: Cascade)
  objectId   String @db.ObjectId
  objectType String // 'contact', 'company', 'custom_object', etc.
  position   Float? // For manual ordering
  
  // Context about who added this item and when
  addedBy    String   @db.ObjectId
  adder      User     @relation(fields: [addedBy], references: [id])
  createdAt  DateTime @default(now())

  @@unique([listId, objectId, objectType])
  @@index([objectId, objectType])
  @@index([listId, position])
  @@map("list_item")
}

enum ModelCapability {
  thinking
  vision  
  tools
}

enum ModelProvider {
  azure
  openrouter
}

model Model {
  id           String             @id @map("_id") @db.ObjectId
  name         String             // Display name (e.g., "GPT-4o Mini")
  model        String             // Model identifier (e.g., "gpt-4o-mini")
  provider     ModelProvider      // AI provider (azure, openrouter)
  searchField  String             // Searchable field for model discovery
  icon         String             // Model icon identifier (anthropic, openai, etc.)
  
  // Model capabilities and settings
  capabilities ModelCapability[]  @default([])
  description  String             // Model description
  isPremium    Boolean            @default(false)  // Premium model flag
  isDisabled   Boolean            @default(false)  // Can be disabled by admin
  cost         Int                @default(0)      // Credits cost per use in CRM
  
  // Relations
  messages     Message[]          // Messages generated with this model
  users        User[]             // Users who have selected this model
  
  // CRM audit fields
  createdAt    DateTime           @default(now())
  updatedAt    DateTime           @updatedAt

  @@unique([model, provider])     // Ensure unique model per provider
  @@index([name])
  @@index([model])
  @@index([provider])
  @@index([isPremium])
  @@index([isDisabled])
  @@map("model")
}

model Note {
  id             String   @id @map("_id") @db.ObjectId
  orgId          String   @db.ObjectId
  organization   Organization @relation(fields: [orgId], references: [id], onDelete: Cascade)
  userId         String   @db.ObjectId
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  title          String
  isArchived     Boolean  @default(false)
  parentDocument String?  @db.ObjectId
  parent         Note?    @relation("NoteParent", fields: [parentDocument], references: [id], onDelete: NoAction, onUpdate: NoAction)
  children       Note[]   @relation("NoteParent")
  content        String?
  coverImage     String?
  icon           String?
  objectId       String?   @db.ObjectId
  objectType     String?
  isPublished    Boolean  @default(false)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  deletedAt      DateTime?
  deletedBy      String?  @db.ObjectId
  isDeleted      Boolean  @default(false)

  @@index([userId], name: "by_userId")
  @@index([orgId], name: "by_orgId")
  @@index([userId, parentDocument], name: "by_userId_parentDocument")
  @@map("note")
}

model Notification {
  id             String   @id @map("_id") @db.ObjectId
  userId         String   @db.ObjectId
  organizationId String?  @db.ObjectId
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  type           String
  title          String
  body           String?
  data           Json?
  read           Boolean  @default(false)
  archived       Boolean  @default(false)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([userId], name: "by_user")
  @@index([organizationId], name: "by_organization")
  @@map("notification")
}

model NotificationSettings {
  id               String   @id @map("_id") @db.ObjectId
  userId           String   @db.ObjectId @unique
  user             User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  emailMentions    Boolean  @default(true)
  emailComments    Boolean  @default(false)
  emailActivities  Boolean  @default(false)
  pushMentions     Boolean  @default(true)
  pushComments     Boolean  @default(false)
  pushActivities   Boolean  @default(false)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  @@map("notification_settings")
}

enum ObjectViewType {
  table
  kanban
  map
}

model ObjectView {
  id               String   @id @map("_id") @default(auto()) @db.ObjectId
  name             String
  objectType       String   // 'contact', 'company', 'property', 'custom_object', etc.
  organizationId   String   @db.ObjectId
  organization     Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  // Column definitions
  columnDefs       Json     // Array of { field: string, headerName: string, width: number }
  cardRowFields    Json?    // Array of { field: string, headerName: string, type?: string } for kanban card rows
  showAttributeLabels Boolean? @default(true) // Whether to show field labels in kanban card rows
  
  // Filters
  filters          Json?    // Array of filter objects
  filterCondition  String?  // 'and' or 'or'

  // View settings
  viewType         ObjectViewType   @default(table)
  statusAttribute  String?  // For kanban views - which field to use for columns
  kanbanConfig     Json?    // Kanban-specific configuration { hiddenColumns: string[], customStatuses: object[] }
  sortBy           String?  // Field to sort by
  sortDirection    String?  // 'asc' or 'desc'
  mapConfig        Json?    // Map-specific configuration { showBoundaries: boolean, showPins: boolean }, tableConfig: { displayType: 'table' | 'grid', rowDensity: 'compact' | 'normal' | 'comfortable', showExportOptions: boolean, allowColumnReorder: boolean, showSearchBar: boolean }
  
  // Access control
  createdBy        String   @db.ObjectId
  creator          User     @relation("ObjectViewCreator", fields: [createdBy], references: [id])
  isDefault        Boolean  @default(false)
  isPublic         Boolean  @default(false) // Can other users see this view
  
  // User preferences
  userPreferences  UserViewPreference[]
  
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  @@index([organizationId])
  @@index([organizationId, objectType])
  @@index([organizationId, createdBy])
  @@map("object_view")
}

model ObjectTag {
  id         String @id @map("_id") @db.ObjectId
  tagId      String @db.ObjectId
  tag        Tag    @relation(fields: [tagId], references: [id], onDelete: Cascade)
  objectId   String @db.ObjectId
  objectType String // 'contact', 'company', 'property', etc.
  
  // Relations
  contact    Contact? @relation(fields: [objectId], references: [id], onDelete: Cascade)
  company    Company? @relation(fields: [objectId], references: [id], onDelete: Cascade)
  property   Property? @relation(fields: [objectId], references: [id], onDelete: Cascade)
  
  // Context about who added this tag and when
  addedBy    String   @db.ObjectId
  adder      User     @relation(fields: [addedBy], references: [id])
  createdAt  DateTime @default(now())

  @@unique([tagId, objectId, objectType])
  @@index([objectId, objectType])
  @@index([tagId])
  @@map("object_tag")
}

model ObjectStatusHistory {
  id             String   @id @map("_id") @db.ObjectId
  objectId       String   @db.ObjectId
  objectType     String   // 'contact', 'company', 'property', etc.
  statusField    String   // 'status', 'stage', etc. - which field was changed
  fromStatus     String?  // Previous status value (null for creation)
  toStatus       String   // New status value
  userId         String   @db.ObjectId
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  organizationId String   @db.ObjectId
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Task relation

  @@index([objectId, objectType])
  @@index([organizationId])
  @@index([objectId, objectType, statusField])
  @@map("object_status_history")
}

model Invitation {
  id             String       @id @map("_id") @db.ObjectId
  organizationId String       @db.ObjectId
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  email          String
  role           String?
  status         String
  expiresAt      DateTime
  inviterId      String       @db.ObjectId
  user           User         @relation(fields: [inviterId], references: [id], onDelete: Cascade)

  @@map("invitation")
}

model Member {
  id             String       @id @map("_id") @db.ObjectId
  organizationId String       @db.ObjectId
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  userId         String       @db.ObjectId
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  role           String
  createdAt      DateTime

  @@unique([userId, organizationId])
  @@map("member")
}

model Organization {
  id                 String       @id @map("_id") @db.ObjectId
  name               String
  slug               String?
  logo               String?
  createdAt          DateTime
  metadata           String?
  paymentsCustomerId String?
  
  // Email settings
  emailWatermarkEnabled Boolean @default(true) // Always true for free accounts, optional for paid
  
  members            Member[]
  invitations        Invitation[]
  purchases          Purchase[]
  notes              Note[]
  chats              Chat[]
  
  // Custom Object System relations
  customFieldDefinitions CustomFieldDefinition[]
  customObjects          CustomObject[]
  contacts               Contact[]
  companies              Company[]
  properties             Property[]
  tags                   Tag[]
  lists                  List[]
  objectViews            ObjectView[]
  relatedContacts        RelatedContact[]
  linkedContacts         LinkedContact[]
  
  // Relations
  creditPurchases CreditPurchase[]
  aiUsage         AiUsage[]
  userOrgCredits  UserOrganizationCredits[]
  forwardedEmails ForwardedEmail[]
  forwardingEmailConfig ForwardingEmailConfig?
  emailActivities EmailActivity[]
  activities      Activity[]
  linkedProperties LinkedProperty[]
  objectRelationships ObjectRelationship[]

  // File management relations
  folders         Folder[]
  files           File[]
  fileAttachments FileAttachment[]
  googleDriveIntegration GoogleDriveIntegration?

  @@unique([slug])
  @@map("organization")
}

enum PurchaseType {
  subscription
  one_time
}

model Purchase {
  id             String        @id @map("_id") @db.ObjectId
  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String?       @db.ObjectId
  user           User?         @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId         String?       @db.ObjectId
  type           PurchaseType
  customerId     String
  subscriptionId String?
  productId      String
  status         String?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt

  // Relations
  creditPurchases CreditPurchase[]

  @@index([subscriptionId])
  @@map("purchase")
}

model Pin {
  id             String   @id @map("_id") @db.ObjectId
  userId         String   @db.ObjectId
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  organizationId String   @db.ObjectId
  objectType     String   // 'contact', 'company', 'property', etc.
  objectId       String   @db.ObjectId
  name           String   // Display name for the pin
  image          String?  // Image/avatar URL for the pin
  position       Float?   // For ordering pins
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@unique([userId, organizationId, objectId, objectType])
  @@index([userId, organizationId])
  @@index([organizationId])
  @@index([userId, organizationId, objectType])
  @@map("pin")
}

model Property {
  id             String   @id @map("_id") @db.ObjectId
  apolloId       String?
  
  // Basic Info
  name           String
  recordType     String   @default("property")
  image          String?
  
  // Organization
  organizationId String   @db.ObjectId
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  // Property Classification
  propertyType    String?  // residential, commercial, land, industrial, mixed_use, etc.
  propertySubType String?
  market          String?
  subMarket       String?
  listingId       String?
  status          String?
  
  // Simple relationships
  objectTags       ObjectTag[]
  
  // Related models (one-to-one or one-to-many)
  location        PropertyLocation?
  physicalDetails PropertyPhysicalDetails?
  financials      PropertyFinancials?
  flags           PropertyFlags?
  mlsData         PropertyMLS?
  legalInfo       PropertyLegal?
  
  // Existing proper Prisma relations
  unitMixes       PropertyUnitMix[]
  saleHistory     PropertySaleHistory[]
  mortgages       PropertyMortgage[]
  demographics    PropertyDemographics?
  foreclosureInfo PropertyForeclosureInfo[]
  mlsHistory      PropertyMlsHistory[]
  linkedProperties LinkedProperty[]
  
  // Audit fields
  createdBy      String   @db.ObjectId
  creator        User     @relation("PropertyCreator", fields: [createdBy], references: [id])
  updatedBy      String?  @db.ObjectId
  updater        User?    @relation("PropertyUpdater", fields: [updatedBy], references: [id])
  lastViewedAt   DateTime?
  lastViewedBy   String?  @db.ObjectId
  lastViewer     User?    @relation("PropertyViewer", fields: [lastViewedBy], references: [id])
  
  // Soft delete
  isDeleted      Boolean  @default(false)
  deletedAt      DateTime?
  deletedBy      String?  @db.ObjectId
  deleter        User?    @relation("PropertyDeleter", fields: [deletedBy], references: [id])
  
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([organizationId])
  @@index([organizationId, isDeleted])
  @@index([organizationId, createdAt])
  @@index([organizationId, name])
  @@index([organizationId, propertyType])
  @@index([organizationId, status])
  @@map("property")
}

model LinkedContact {
  id             String   @id @map("_id") @db.ObjectId
  
  // Contact relationships
  contact1Id     String   @db.ObjectId
  contact1       Contact  @relation("LinkedContactAsContact1", fields: [contact1Id], references: [id], onDelete: Cascade)
  contact1Relation String // e.g., "Spouse", "Business Partner", "Colleague", "Friend", "Family", "Client"
  
  contact2Id     String   @db.ObjectId  
  contact2       Contact  @relation("LinkedContactAsContact2", fields: [contact2Id], references: [id], onDelete: Cascade)
  contact2Relation String // e.g., "Spouse", "Business Partner", "Colleague", "Friend", "Family", "Client"
  
  // Organization
  organizationId String   @db.ObjectId
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  // Audit fields
  createdBy      String   @db.ObjectId
  creator        User     @relation("LinkedContactCreator", fields: [createdBy], references: [id])
  updatedBy      String?  @db.ObjectId
  updater        User?    @relation("LinkedContactUpdater", fields: [updatedBy], references: [id])
  
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([contact1Id])
  @@index([contact2Id]) 
  @@index([organizationId])
  @@unique([contact1Id, contact2Id, organizationId]) // Prevent duplicate links
  @@map("linked_contact")
}

model LinkedProperty {
  id           String   @id @map("_id") @db.ObjectId
  
  // Relationship details
  relation     String   // e.g., "owner", "agent", "buyer", "seller", "tenant", "landlord"
  
  // Foreign keys
  contactId    String   @db.ObjectId
  contact      Contact  @relation(fields: [contactId], references: [id], onDelete: Cascade)
  
  propertyId   String   @db.ObjectId
  property     Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  
  organizationId String @db.ObjectId
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  // Audit fields
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  createdBy    String?  @db.ObjectId
  updatedBy    String?  @db.ObjectId
  creator      User?    @relation("LinkedPropertyCreatedBy", fields: [createdBy], references: [id], onDelete: SetNull)
  updater      User?    @relation("LinkedPropertyUpdatedBy", fields: [updatedBy], references: [id], onDelete: SetNull)
  
  // Indexes
  @@index([contactId])
  @@index([propertyId])
  @@index([organizationId])
  @@index([contactId, propertyId])
  @@index([organizationId, relation])
  
  @@map("linked_property")
}

// Dynamic object relationship model
model ObjectRelationship {
  id             String   @id @map("_id") @db.ObjectId
  
  // First object (source)
  object1Id      String   @db.ObjectId
  object1Type    String   // The type of the first object (contact, property, company, etc.)
  object1Role    String   // The role/relationship from object1's perspective (e.g., "father", "owner", "agent")
  
  // Second object (target)
  object2Id      String   @db.ObjectId
  object2Type    String   // The type of the second object (contact, property, company, etc.)
  object2Role    String   // The role/relationship from object2's perspective (e.g., "son", "owned_by", "client")
  
  // Metadata
  metadata       Json?    // Additional relationship metadata (strength, notes, etc.)
  isActive       Boolean  @default(true)
  
  // Organization scoping
  organizationId String   @db.ObjectId
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  // Audit fields
  createdBy      String   @db.ObjectId
  creator        User     @relation("ObjectRelationshipCreator", fields: [createdBy], references: [id])
  updatedBy      String?  @db.ObjectId
  updater        User?    @relation("ObjectRelationshipUpdater", fields: [updatedBy], references: [id])
  
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([object1Id, object1Type])
  @@index([object2Id, object2Type])
  @@index([organizationId])
  @@index([organizationId, object1Type, object2Type])
  @@index([organizationId, object1Type])
  @@index([organizationId, object2Type])
  @@index([organizationId, isActive])
  @@unique([object1Id, object1Type, object2Id, object2Type, organizationId]) // Prevent duplicate relationships

  @@map("object_relationship")
}

model PropertyLocation {
  id         String   @id @map("_id") @db.ObjectId
  propertyId String   @unique @db.ObjectId
  property   Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  
  // Address stored as JSON for complex structure
  address    Json?    // { street, street2, city, state, zip, county, country }
  location   Json?    // GeoJSON: { type: "Point", coordinates: [lng, lat] }
  website    String?
  
  // Geographic details
  neighborhood Json?  // { location: { coordinates: [lng, lat], type: "Point" }, id, name, type }
  county       String?
  subdivision  String?
  lotNumber    String?
  parcelNumber String?
  zoning       String?
  
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  
  @@map("property_location")
}

model PropertyPhysicalDetails {
  id         String   @id @map("_id") @db.ObjectId
  propertyId String   @unique @db.ObjectId
  property   Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  
  // Basic measurements
  yearBuilt         Int?
  squareFootage     Int?
  units             Int?
  floors            Int?
  structures        Int?
  
  // Room details
  bedrooms          Int?
  bathrooms         Int?
  roomsCount        Int?
  
  // Square footage breakdown
  buildingSquareFeet Int?
  garageSquareFeet   Int?
  livingSquareFeet   Int?
  lotSquareFeet      Int?
  
  // Lot information
  lotSize           Float?
  lotType           String?
  lotAcres          Float?
  
  // Construction details
  construction      String?
  primaryUse        String?
  propertyUse       String?
  class             String?
  
  // Parking & garage
  parking           String?
  parkingSpaces     Int?
  garageType        String?
  
  // Utilities & systems
  heatingType       String?
  meterType         String?
  
  // Legal
  legalDescription  String?
  
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  @@map("property_physical_details")
}

model PropertyFinancials {
  id         String   @id @map("_id") @db.ObjectId
  propertyId String   @unique @db.ObjectId
  property   Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  
  // Pricing
  price                Float?
  estimatedValue       Float?
  pricePerSquareFoot   Float?
  
  // Equity
  equity               Float?
  equityPercent        Float?
  estimatedEquity      Float?
  
  // Sale information
  saleDate             DateTime?
  salePrice            Float?
  lastSalePrice        Float?
  lastSaleDate         DateTime?
  
  // Property values
  landValue            Float?
  buildingValue        Float?
  
  // Investment metrics
  cap                  Float?
  exchange             Boolean?
  exchangeId           String?
  
  // Tax information
  taxInfo              Json?    // Tax assessment and value information
  
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
  
  @@map("property_financials")
}

model PropertyFlags {
  id         String   @id @map("_id") @db.ObjectId
  propertyId String   @unique @db.ObjectId
  property   Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  
  // Ownership flags
  absenteeOwner           Boolean @default(false)
  inStateAbsenteeOwner    Boolean @default(false)
  outOfStateAbsenteeOwner Boolean @default(false)
  ownerOccupied           Boolean @default(false)
  corporateOwned          Boolean @default(false)
  
  // Property condition/status flags
  vacant                  Boolean @default(false)
  mobileHome              Boolean @default(false)
  carport                 Boolean @default(false)
  
  // Financial/legal flags
  auction                 Boolean @default(false)
  cashBuyer              Boolean @default(false)
  investorBuyer          Boolean @default(false)
  freeClear              Boolean @default(false)
  highEquity             Boolean @default(false)
  privateLender          Boolean @default(false)
  
  // Legal/deed flags
  deedInLieu             Boolean @default(false)
  quitClaim              Boolean @default(false)
  sheriffsDeed           Boolean @default(false)
  warrantyDeed           Boolean @default(false)
  inherited              Boolean @default(false)
  spousalDeath           Boolean @default(false)
  
  // Lien/foreclosure flags
  lien                   Boolean @default(false)
  taxLien                Boolean @default(false)
  preForeclosure         Boolean @default(false)
  trusteeSale            Boolean @default(false)
  
  // Environmental flags
  floodZone              Boolean @default(false)
  
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt
  
  @@map("property_flags")
}

model PropertyMLS {
  id         String   @id @map("_id") @db.ObjectId
  propertyId String   @unique @db.ObjectId
  property   Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  organizationId String @db.ObjectId
  
  // MLS Status flags
  mlsActive              Boolean @default(false)
  mlsCancelled           Boolean @default(false)
  mlsFailed              Boolean @default(false)
  mlsHasPhotos           Boolean @default(false)
  mlsPending             Boolean @default(false)
  mlsSold                Boolean @default(false)
  
  // MLS metrics
  mlsDaysOnMarket        Int?
  mlsListingPrice        Float?
  mlsListingPricePerSquareFoot Float?
  mlsSoldPrice           Float?
  
  // MLS details
  mlsStatus              String?
  mlsType                String?
  mlsListingDate         String?
  
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt
  
  @@map("property_mls")
}

model PropertyLegal {
  id         String   @id @map("_id") @db.ObjectId
  propertyId String   @unique @db.ObjectId
  property   Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  
  // Environmental
  floodZoneDescription   String?
  floodZoneType          String?
  
  // Legal notices
  noticeType             String?
  
  // Identification
  reaId                  String?
  
  // Timestamps
  lastUpdateDate         String?
  
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt
  
  @@map("property_legal")
}

model PropertyUnitMix {
  id          String   @id @map("_id") @db.ObjectId
  propertyId  String   @db.ObjectId
  property    Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  organizationId String @db.ObjectId
  
  // Unit mix details
  name        String
  units       Float?
  minSquareFootage Float?
  maxSquareFootage Float?
  minPrice    Float?
  maxPrice    Float?
  minRent     Float?
  maxRent     Float?
  
  // Audit fields
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@index([propertyId])
  @@index([organizationId])
  @@map("property_unit_mix")
}

model PropertySaleHistory {
  id          String   @id @map("_id") @db.ObjectId
  propertyId  String   @db.ObjectId
  property    Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  organizationId String @db.ObjectId
  
  // Sale details
  seller      String?  // Can be contact ID, company ID, or name string
  buyer       String?  // Can be contact ID, company ID, or name string
  saleDate    DateTime?
  salePrice   Float?
  askingPrice Float?
  transactionType String?
  pricePerSquareFoot Float?
  pricePerUnit Float?
  transferredOwnershipPercentage Float?
  capRate     Float?
  grmRate     Float?
  
  // Audit fields
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@index([propertyId])
  @@index([organizationId])
  @@map("property_sale_history")
}

model PropertyMortgage {
  id          String   @id @map("_id") @db.ObjectId
  propertyId  String   @db.ObjectId
  property    Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  organizationId String @db.ObjectId
  
  // Mortgage details
  amount      Float?
  assumable   Boolean?
  deedType    Json?
  documentDate String?
  documentNumber Json?
  granteeName String?
  interestRate Float?
  interestRateType String?
  lenderCode  String?
  lenderName  String?
  lenderType  String?
  loanType    String?
  loanTypeCode String?
  maturityDate String?
  mortgageId  Int?
  open        Boolean?
  position    String?
  recordingDate String?
  seqNo       Int?
  term        Int?
  termType    String?
  transactionType String?
  
  // Audit fields
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@index([propertyId])
  @@index([organizationId])
  @@map("property_mortgage")
}

model PropertyDemographics {
  id          String   @id @map("_id") @db.ObjectId
  propertyId  String   @unique @db.ObjectId
  property    Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  organizationId String @db.ObjectId
  
  // Demographics details
  fmrEfficiency Float?
  fmrFourBedroom Float?
  fmrOneBedroom Float?
  fmrThreeBedroom Float?
  fmrTwoBedroom Float?
  fmrYear     Float?
  hudAreaCode String?
  hudAreaName String?
  medianIncome Float?
  suggestedRent Float?
  
  // Audit fields
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@index([organizationId])
  @@map("property_demographics")
}

model PropertyForeclosureInfo {
  id          String   @id @map("_id") @db.ObjectId
  propertyId  String   @db.ObjectId
  property    Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  organizationId String @db.ObjectId
  
  // Foreclosure details
  foreclosureId Json?
  originalLoanAmount Float?
  estimatedBankValue Float?
  defaultAmount Float?
  recordingDate String?
  openingBid  Float?
  auctionDate String?
  auctionTime String?
  auctionStreetAddress String?
  documentType String?
  trusteeSaleNumber Json?
  typeName    String?
  active      Boolean?
  lenderName  String?
  lenderPhone String?
  noticeType  String?
  seqNo       Int?
  trusteeAddress String?
  trusteeName String?
  trusteePhone String?
  judgmentDate String?
  judgmentAmount Float?
  
  // Audit fields
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@index([propertyId])
  @@index([organizationId])
  @@map("property_foreclosure_info")
}

model PropertyMlsHistory {
  id          String   @id @map("_id") @db.ObjectId
  propertyId  String   @db.ObjectId
  property    Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  organizationId String @db.ObjectId
  
  mlsId       Int?
  type        String?
  price       Float?
  beds        Int?
  baths       Int?
  daysOnMarket Int?
  agentName   String?  // Can be contact ID or name string
  agentOffice String?  // Can be company ID or name string
  agentPhone  String?
  agentEmail  String?
  status      String?
  statusDate  String?
  lastStatusDate String?
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@index([propertyId])
  @@index([organizationId])
  @@map("property_mls_history")
}

enum TagRole {
  viewer
  editor
  admin
}

enum ListRole {
  viewer
  editor
  admin
}

model Tag {
  id             String   @id @map("_id") @db.ObjectId
  name           String
  color          String?
  organizationId String   @db.ObjectId
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  objectType     String   // 'contact', 'company', 'property', etc.
  
  // Relations
  objectTags     ObjectTag[]
  permissions    TagPermission[]
  
  // Audit fields
  createdBy      String   @db.ObjectId
  creator        User     @relation("TagCreator", fields: [createdBy], references: [id])
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@unique([name, organizationId, objectType])
  @@index([organizationId])
  @@index([organizationId, objectType])
  @@map("tag")
}

model TagPermission {
  id     String   @id @map("_id") @db.ObjectId
  tagId  String   @db.ObjectId
  tag    Tag      @relation(fields: [tagId], references: [id], onDelete: Cascade)
  userId String   @db.ObjectId
  user   User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  role   TagRole
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([tagId, userId])
  @@index([userId])
  @@map("tag_permission")
}

enum TaskStatus {
  backlog
  todo
  in_progress
  review
  done
}

enum TaskPriority {
  no_priority
  urgent
  high
  medium
  low
}

model Task {
  id             String          @id @map("_id") @db.ObjectId
  title          String
  description    String?
  createdById    String          @db.ObjectId
  createdBy      User            @relation("TaskCreatedBy", fields: [createdById], references: [id], onDelete: Cascade)
  dueDate        DateTime?
  status         TaskStatus      @default(todo)
  assigneeId     String?         @db.ObjectId
  assignee       User?           @relation("TaskAssignee", fields: [assigneeId], references: [id], onDelete: SetNull)
  
  // Related object reference (replaces relatedIds array)
  relatedObjectId   String?      @db.ObjectId
  relatedObjectType String?      // 'contact', 'company', 'custom_object', etc.
  
  organizationId String          @db.ObjectId
  priority       TaskPriority    @default(no_priority)
  position       Float?
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt

  @@index([organizationId], name: "by_organization")
  @@index([assigneeId], name: "by_assignee")
  @@index([status], name: "by_status")
  @@index([dueDate], name: "by_due_date")
  @@index([position], name: "by_position")
  @@index([status, position], name: "by_status_position")
  @@index([relatedObjectId, relatedObjectType], name: "by_related_object")
  @@index([organizationId, relatedObjectType], name: "by_org_object_type")
  @@map("task")
}

model ColumnPreference {
  id                 String   @id @map("_id") @db.ObjectId
  organizationId     String   @db.ObjectId
  column             String
  trackTimeInStatus  Boolean
  showConfetti       Boolean
  hidden             Boolean
  targetTimeInStatus Float?
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  @@index([organizationId, column], name: "by_organization_column")
  @@map("column_preference")
}

model Account {
  id           String    @id @map("_id") @db.ObjectId
  accountId    String
  providerId   String
  userId       String    @db.ObjectId
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken  String?
  refreshToken String?
  idToken      String?
  expiresAt    DateTime?
  password     String?

  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model UserOrganizationCredits {
  id               String       @id @map("_id") @db.ObjectId
  userId           String       @db.ObjectId
  user             User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  organizationId   String       @db.ObjectId
  organization     Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  // Credits system fields
  creditsTotal     Int          @default(10)  // Total credits allocated (reset daily for free users)
  creditsUsed      Int          @default(0)   // Credits used this period
  creditsResetAt   DateTime     @default(now()) // When credits were last reset
  creditsPurchased Int          @default(0)   // Credits purchased (never expire)
  
  createdAt        DateTime     @default(now())
  updatedAt        DateTime     @updatedAt

  @@unique([userId, organizationId])
  @@index([userId])
  @@index([organizationId])
  @@index([organizationId, userId])
  @@map("user_organization_credits")
}

model Passkey {
  id           String    @id @map("_id") @db.ObjectId
  name         String?
  publicKey    String
  userId       String    @db.ObjectId
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  credentialID String
  counter      Int
  deviceType   String
  backedUp     Boolean
  transports   String?
  createdAt    DateTime?

  @@map("passkey")
}

model User {
  id                 String              @id @map("_id") @db.ObjectId
  name               String
  email              String
  emailVerified      Boolean
  image              String?
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  username           String?
  role               String?
  banned             Boolean?
  banReason          String?
  banExpires         DateTime?
  onboardingComplete Boolean             @default(false)
  paymentsCustomerId String?
  locale             String?
  sessions           Session[]
  accounts           Account[]
  passkeys           Passkey[]
  invitations        Invitation[]
  purchases          Purchase[]
  memberships        Member[]
  feedbacks          Feedback[]
  createdTasks       Task[]              @relation("TaskCreatedBy")
  assignedTasks      Task[]              @relation("TaskAssignee")
  objectStatusHistories ObjectStatusHistory[]
  favorites          Favorite[]
  favoriteFolders    FavoriteFolder[]
  notifications      Notification[]
  notes              Note[]
  
  // Custom Object relations
  createdCustomObjects   CustomObject[]  @relation("CustomObjectCreator")
  viewedCustomObjects    CustomObject[]  @relation("CustomObjectViewer")
  deletedCustomObjects   CustomObject[]  @relation("CustomObjectDeleter")
  
  // Contact relations
  createdContacts        Contact[]       @relation("ContactCreator")
  updatedContacts        Contact[]       @relation("ContactUpdater")
  viewedContacts         Contact[]       @relation("ContactViewer")
  deletedContacts        Contact[]       @relation("ContactDeleter")
  createdRelatedContacts RelatedContact[] @relation("RelatedContactCreator")
  
  // Company relations
  createdCompanies       Company[]       @relation("CompanyCreator")
  updatedCompanies       Company[]       @relation("CompanyUpdater")
  deletedCompanies       Company[]       @relation("CompanyDeleter")
  
  // Property relations
  createdProperties      Property[]      @relation("PropertyCreator")
  updatedProperties      Property[]      @relation("PropertyUpdater")
  viewedProperties       Property[]      @relation("PropertyViewer")
  deletedProperties      Property[]      @relation("PropertyDeleter")
  
  // LinkedProperty relations
  createdLinkedProperties LinkedProperty[] @relation("LinkedPropertyCreatedBy")
  updatedLinkedProperties LinkedProperty[] @relation("LinkedPropertyUpdatedBy")
  
  // LinkedContact relations
  createdLinkedContacts  LinkedContact[] @relation("LinkedContactCreator")
  updatedLinkedContacts  LinkedContact[] @relation("LinkedContactUpdater")
  
  // ObjectRelationship relations
  createdObjectRelationships ObjectRelationship[] @relation("ObjectRelationshipCreator")
  updatedObjectRelationships ObjectRelationship[] @relation("ObjectRelationshipUpdater")
  
  // Tag relations
  createdTags            Tag[]           @relation("TagCreator")
  tagPermissions         TagPermission[]
  addedObjectTags        ObjectTag[]
  
  // List relations
  createdLists           List[]          @relation("ListCreator")
  listPermissions        ListPermission[]
  addedListItems         ListItem[]
  
  // ObjectView relations
  createdObjectViews     ObjectView[]    @relation("ObjectViewCreator")
  userViewPreferences    UserViewPreference[]
  
  // Pin relations
  pins                   Pin[]
  
  // Model selection
  modelId          String?  @db.ObjectId // Selected model ID
  selectedModel    Model?   @relation(fields: [modelId], references: [id])
  
  // User preferences
  preferences      Json?    // { nickname?: string, biography?: string, instructions?: string }
  appearance       Json?    // { mode?: "light" | "dark", theme?: string }
  
  // Relations
  creditPurchases  CreditPurchase[]
  aiUsage          AiUsage[]
  chats            Chat[]            // AI chat sessions  
  messages         Message[]         // User's AI messages
  userOrgCredits   UserOrganizationCredits[]
  forwardedEmails  ForwardedEmail[]
  emailActivities  EmailActivity[]
  activities       Activity[]
  activityReplies  ActivityReply[]
  activityReactions ActivityReaction[]
  resolvedActivities Activity[]      @relation("ActivityResolver")
  notificationSettings NotificationSettings?

  // File management relations
  createdFolders         Folder[]        @relation("FolderCreator")
  updatedFolders         Folder[]        @relation("FolderUpdater")
  deletedFolders         Folder[]        @relation("FolderDeleter")
  uploadedFiles          File[]          @relation("FileUploader")
  updatedFiles           File[]          @relation("FileUpdater")
  accessedFiles          File[]          @relation("FileAccessor")
  deletedFiles           File[]          @relation("FileDeleter")
  folderPermissions      FolderPermission[]
  filePermissions        FilePermission[]
  fileAttachments        FileAttachment[]
  googleDriveIntegrations GoogleDriveIntegration[]

  @@unique([email])
  @@index([username])
  @@map("user")
}

model UserViewPreference {
  id             String   @id @map("_id") @db.ObjectId
  userId         String   @db.ObjectId
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  organizationId String   @db.ObjectId
  objectType     String   // 'contact', 'company', 'property', etc.
  viewId         String   @db.ObjectId
  view           ObjectView @relation(fields: [viewId], references: [id], onDelete: Cascade)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@unique([userId, organizationId, objectType])
  @@index([userId])
  @@index([organizationId])
  @@index([userId, organizationId])
  @@map("user_view_preference")
}

model Session {
  id        String   @id @map("_id") @db.ObjectId
  expiresAt DateTime
  ipAddress String?
  userAgent String?
  userId    String   @db.ObjectId
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  impersonatedBy String?

  activeOrganizationId String? @db.ObjectId

  token     String
  createdAt DateTime
  updatedAt DateTime

  @@unique([token])
  @@map("session")
}

model Verification {
  id         String   @id @map("_id") @db.ObjectId
  identifier String
  value      String
  expiresAt  DateTime

  createdAt DateTime?
  updatedAt DateTime?

  @@map("verification")
}

model ForwardedEmail {
  id             String   @id @map("_id") @db.ObjectId
  organizationId String   @db.ObjectId
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  // Email metadata
  messageId      String   @unique
  from           String
  to             String
  subject        String
  body           String   // HTML or plain text body
  attachments    Json?    // Array of attachment objects
  headers        Json?    // Full email headers
  
  // Processing metadata
  participants   String[] // All email addresses found in the email
  processedAt    DateTime?
  linkedRecords  Json?    // IDs of contacts/companies created or linked: { contacts: ["id1", "id2"], companies: ["id3"] }
  forwardedBy    String   @db.ObjectId // Which user forwarded this email
  forwarder      User     @relation(fields: [forwardedBy], references: [id])
  
  // Email forwarding settings
  sharingLevel   String   @default("full") // "metadata", "subject", "full"
  isBlocked      Boolean  @default(false)
  
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([organizationId])
  @@index([organizationId, from])
  @@index([organizationId, createdAt])
  @@map("forwarded_email")
}

model ForwardingEmailConfig {
  id             String   @id @map("_id") @db.ObjectId
  organizationId String   @unique @db.ObjectId
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  // Email configuration
  address        String   // e.g., "<EMAIL>"
  isActive       Boolean  @default(true)
  
  // Sharing settings
  defaultSharingLevel String @default("full") // "metadata", "subject", "full"
  
  // Individual sharing permissions (JSON array of user IDs with their permissions)
  individualSharing Json? // [{ userId: "123", level: "full" }, ...]
  
  // Blocklist
  blockedEmails    String[] @default([])
  blockedDomains   String[] @default([])
  
  // Processing settings
  autoCreateContacts Boolean @default(true)
  autoCreateCompanies Boolean @default(true)
  
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  
  @@map("forwarding_email_config")
}

enum EmailSourceType {
  connected_account
  forwarded
  manual
}

enum EmailDirection {
  inbound
  outbound
}

enum ActivityType {
  note
  call
  email
  meeting
  task
  system
}

model Activity {
  id             String   @id @map("_id") @db.ObjectId
  organizationId String   @db.ObjectId
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  userId         String   @db.ObjectId
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Related object reference
  recordId       String?  @db.ObjectId
  recordType     String?  // 'contact', 'company', 'property', etc.
  
  // Activity details
  type           ActivityType @default(note)
  message        String
  resolved       Boolean  @default(false)
  resolvedBy     String?  @db.ObjectId
  resolver       User?    @relation("ActivityResolver", fields: [resolvedBy], references: [id])
  
  // Call-specific fields
  phone          String?
  result         String?
  
  // System activity flag
  system         Boolean  @default(false)
  
  // Edit tracking
  edited         Boolean  @default(false)
  editedAt       DateTime?
  
  // Mentions
  mentionedUsers String[] @default([]) // User IDs
  
  // Relations
  replies        ActivityReply[]
  reactions      ActivityReaction[]
  
  // Audit fields
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([organizationId])
  @@index([organizationId, recordId])
  @@index([organizationId, recordType])
  @@index([organizationId, userId])
  @@index([organizationId, type])
  @@index([recordId, recordType])
  @@index([userId])
  @@map("activity")
}

model ActivityReply {
  id         String   @id @map("_id") @db.ObjectId
  activityId String   @db.ObjectId
  activity   Activity @relation(fields: [activityId], references: [id], onDelete: Cascade)
  userId     String   @db.ObjectId
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  message    String
  
  // Edit tracking
  edited     Boolean  @default(false)
  editedAt   DateTime?
  
  // Mentions
  mentionedUsers String[] @default([]) @db.ObjectId
  
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@index([activityId])
  @@index([userId])
  @@map("activity_reply")
}

model EmailActivity {
  id             String   @id @map("_id") @db.ObjectId
  organizationId String   @db.ObjectId
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  userId         String   @db.ObjectId
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Source information
  sourceType     EmailSourceType
  sourceId       String?  @db.ObjectId // Account.id or ForwardedEmail.id
  
  // Email metadata
  messageId      String   // External message ID from email provider
  threadId       String?  // For grouping related emails
  inReplyTo      String?  // Message ID this email is replying to
  references     String[] @default([]) // Array of referenced message IDs
  
  // Email content
  from           String
  to             String[] @default([])
  cc             String[] @default([])
  bcc            String[] @default([])
  subject        String
  body           String   // HTML or plain text body
  bodyPlain      String?  // Plain text version if body is HTML
  attachments    Json?    // Array of attachment objects
  headers        Json?    // Full email headers
  
  // Email properties
  direction      EmailDirection
  timestamp      DateTime // Original email timestamp
  isRead         Boolean  @default(false)
  isImportant    Boolean  @default(false)
  
  // Processing metadata
  processedAt    DateTime?
  processingNotes String? // Any notes from automatic processing
  
  // Audit fields
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([organizationId, timestamp])
  @@index([organizationId, userId])
  @@index([messageId])
  @@index([threadId])
  @@index([from])
  @@index([organizationId, sourceType])
  @@index([organizationId, direction])
  @@map("email_activity")
}

model ActivityReaction {
  id             String   @id @map("_id") @db.ObjectId
  activityId     String   @db.ObjectId
  activity       Activity @relation(fields: [activityId], references: [id], onDelete: Cascade)
  userId         String   @db.ObjectId
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  emoji          String   // The emoji character/string
  createdAt      DateTime @default(now())

  @@unique([activityId, userId, emoji]) // One unique emoji reaction per user per activity
  @@index([activityId])
  @@index([userId])
  @@map("activity_reaction")
}

// File Management Models
model Folder {
  id             String   @id @map("_id") @db.ObjectId
  name           String
  description    String?
  parentId       String?  @db.ObjectId
  parent         Folder?  @relation("FolderHierarchy", fields: [parentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  children       Folder[] @relation("FolderHierarchy")

  // Organization scoping
  organizationId String   @db.ObjectId
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  // Scope for organizing attachments within specific records
  scope          String?  // e.g., "contact:123", "company:456", "property:789" for scoped folders

  // Folder state for UI
  isOpen         Boolean  @default(true)

  // Files in this folder
  files          File[]

  // Google Drive integration
  googleDriveId  String?  // Google Drive folder ID if synced

  // Permissions and sharing
  isPublic       Boolean  @default(false)
  permissions    FolderPermission[]

  // Audit fields
  createdBy      String   @db.ObjectId
  creator        User     @relation("FolderCreator", fields: [createdBy], references: [id])
  updatedBy      String?  @db.ObjectId
  updater        User?    @relation("FolderUpdater", fields: [updatedBy], references: [id])

  // Soft delete
  isDeleted      Boolean  @default(false)
  deletedAt      DateTime?
  deletedBy      String?  @db.ObjectId
  deleter        User?    @relation("FolderDeleter", fields: [deletedBy], references: [id])

  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([organizationId])
  @@index([organizationId, parentId])
  @@index([organizationId, isDeleted])
  @@index([organizationId, name])
  @@index([organizationId, scope])
  @@index([googleDriveId])
  @@map("folder")
}

enum FileType {
  document
  image
  video
  audio
  archive
  other
}

model File {
  id             String   @id @map("_id") @db.ObjectId
  name           String
  originalName   String   // Original filename when uploaded
  description    String?

  // File metadata
  size           Int      // File size in bytes
  mimeType       String
  fileType       FileType
  extension      String?

  // Storage information
  url            String   // EdgeStore URL or Google Drive URL
  edgeStoreUrl   String?  // EdgeStore URL if stored locally
  thumbnailUrl   String?  // Thumbnail URL for images/videos

  // Folder organization
  folderId       String?  @db.ObjectId
  folder         Folder?  @relation(fields: [folderId], references: [id], onDelete: SetNull)

  // Organization scoping
  organizationId String   @db.ObjectId
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  // Google Drive integration
  googleDriveId  String?  // Google Drive file ID if synced
  googleDriveUrl String?  // Google Drive sharing URL

  // File attachments to records
  attachments    FileAttachment[]

  // Permissions and sharing
  isPublic       Boolean  @default(false)
  permissions    FilePermission[]

  // Version control
  version        Int      @default(1)
  parentFileId   String?  @db.ObjectId
  parentFile     File?    @relation("FileVersions", fields: [parentFileId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  versions       File[]   @relation("FileVersions")

  // Audit fields
  uploadedBy     String   @db.ObjectId
  uploader       User     @relation("FileUploader", fields: [uploadedBy], references: [id])
  updatedBy      String?  @db.ObjectId
  updater        User?    @relation("FileUpdater", fields: [updatedBy], references: [id])

  // Access tracking
  lastAccessedAt DateTime?
  lastAccessedBy String?  @db.ObjectId
  lastAccessor   User?    @relation("FileAccessor", fields: [lastAccessedBy], references: [id])
  downloadCount  Int      @default(0)

  // Soft delete
  isDeleted      Boolean  @default(false)
  deletedAt      DateTime?
  deletedBy      String?  @db.ObjectId
  deleter        User?    @relation("FileDeleter", fields: [deletedBy], references: [id])

  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([organizationId])
  @@index([organizationId, folderId])
  @@index([organizationId, fileType])
  @@index([organizationId, isDeleted])
  @@index([organizationId, name])
  @@index([googleDriveId])
  @@index([uploadedBy])
  @@index([parentFileId])
  @@map("file")
}

enum PermissionRole {
  viewer
  editor
  admin
}

model FolderPermission {
  id       String         @id @map("_id") @db.ObjectId
  folderId String         @db.ObjectId
  folder   Folder         @relation(fields: [folderId], references: [id], onDelete: Cascade)
  userId   String         @db.ObjectId
  user     User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  role     PermissionRole

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([folderId, userId])
  @@index([userId])
  @@map("folder_permission")
}

model FilePermission {
  id     String         @id @map("_id") @db.ObjectId
  fileId String         @db.ObjectId
  file   File           @relation(fields: [fileId], references: [id], onDelete: Cascade)
  userId String         @db.ObjectId
  user   User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  role   PermissionRole

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([fileId, userId])
  @@index([userId])
  @@map("file_permission")
}

model FileAttachment {
  id             String   @id @map("_id") @db.ObjectId
  fileId         String   @db.ObjectId
  file           File     @relation(fields: [fileId], references: [id], onDelete: Cascade)

  // Attached to which record
  attachedToId   String   @db.ObjectId
  attachedToType String   // 'contact', 'company', 'property', 'task', 'note', etc.

  // Organization scoping
  organizationId String   @db.ObjectId
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  // Context about the attachment
  description    String?
  attachedBy     String   @db.ObjectId
  attacher       User     @relation(fields: [attachedBy], references: [id])

  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([attachedToId, attachedToType])
  @@index([fileId])
  @@index([organizationId])
  @@index([attachedBy])
  @@map("file_attachment")
}

model GoogleDriveIntegration {
  id             String   @id @map("_id") @db.ObjectId

  // Organization scoping
  organizationId String   @db.ObjectId @unique
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  // Google OAuth credentials
  accessToken    String
  refreshToken   String
  tokenExpiresAt DateTime
  scope          String   // OAuth scopes granted

  // Google Drive account info
  googleAccountId String  // Google account ID
  googleEmail     String  // Google account email

  // Sync settings
  isEnabled       Boolean @default(true)
  syncFolderId    String? // Root Google Drive folder for sync
  lastSyncAt      DateTime?
  syncStatus      String  @default("idle") // idle, syncing, error
  syncError       String?

  // Audit fields
  connectedBy     String   @db.ObjectId
  connector       User     @relation(fields: [connectedBy], references: [id])

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([googleAccountId])
  @@map("google_drive_integration")
}