import { PrismaClient } from '@prisma/client';
import mongoose from 'mongoose';
import ContactDetails from '../models/contacts.model.js';
import { ObjectId } from 'mongodb';

// Define the old team/organization model
const TeamSchema = new mongoose.Schema({
  _id: mongoose.Schema.Types.ObjectId,
  name: String,
  slug: String,
  logo: String,
  createdAt: Date,
  emailWatermarkEnabled: Boolean
}, { collection: 'teams' });

const Team = mongoose.model('Team', TeamSchema);

const prisma = new PrismaClient();

interface OldContact {
  _id: string;
  id?: string;
  recordType?: string;
  persona?: string;
  status?: string;
  firstName?: string;
  lastName?: string;
  coordinates?: {
    lat?: number;
    lng?: number;
  };
  isDeleted?: boolean;
  deletedDate?: Date;
  title?: string;
  address?: string;
  address2?: string;
  team?: string;
  city?: string;
  state?: string;
  zipCode?: number;
  county?: string;
  country?: string;
  phone?: string;
  phoneNumbers?: Array<{
    phone: string;
    phoneLabel?: string;
    isBadNumber?: boolean;
    isPrimary?: boolean;
  }>;
  email?: string;
  emails?: Array<{
    email: string;
    emailLabel?: string;
    isBadEmail?: boolean;
    isPrimary?: boolean;
  }>;
  lastViewedAt?: Date;
  lastViewedBy?: string;
  website?: string;
  company?: string;
  profileImage?: string;
  stickyNote?: string;
  source?: string;
  stage?: string;
  birthDate?: Date;
  spouseName?: string;
  createdBy?: string;
  updatedBy?: string;
  createdAt?: Date;
  updatedAt?: Date;
  deleted?: boolean;
  deletedBy?: string;
  tags?: string[];
  relatedContacts?: Array<{
    name?: string;
    email?: string;
    phone?: string;
    label?: string;
  }>;
  buyerNeeds?: Array<{
    minUnits?: number;
    maxUnits?: number;
    minPrice?: number;
    maxPrice?: number;
    propertyType?: string;
    location?: string;
    minBeds?: number;
    maxBeds?: number;
    minBaths?: number;
    maxBaths?: number;
    minSquareFootage?: number;
    maxSquareFootage?: number;
    minLotSize?: number;
    maxLotSize?: number;
    minYearBuilt?: number;
    maxYearBuilt?: number;
    notes?: string;
  }>;
}

// Reuse transformation functions from test script
function transformPhoneNumbers(contact: OldContact) {
  const phones: any[] = [];
  
  if (contact.phone) {
    phones.push({
      number: contact.phone,
      type: 'primary',
      isPrimary: true
    });
  }
  
  if (contact.phoneNumbers?.length) {
    contact.phoneNumbers.forEach(phone => {
      if (phone.phone && phone.phone !== contact.phone) {
        phones.push({
          number: phone.phone,
          type: phone.phoneLabel || 'other',
          isPrimary: phone.isPrimary || false,
          isBad: phone.isBadNumber || false
        });
      }
    });
  }
  
  return phones.length > 0 ? phones : null;
}

function transformEmailAddresses(contact: OldContact) {
  const emails: any[] = [];
  
  if (contact.email) {
    emails.push({
      address: contact.email,
      type: 'primary',
      isPrimary: true
    });
  }
  
  if (contact.emails?.length) {
    contact.emails.forEach(emailObj => {
      if (emailObj.email && emailObj.email !== contact.email) {
        emails.push({
          address: emailObj.email,
          type: emailObj.emailLabel || 'other',
          isPrimary: emailObj.isPrimary || false,
          isBad: emailObj.isBadEmail || false
        });
      }
    });
  }
  
  return emails.length > 0 ? emails : null;
}

function transformAddress(contact: OldContact) {
  const addresses: any[] = [];
  
  if (contact.address || contact.city || contact.state || contact.zipCode) {
    const addressObj: any = {
      type: 'primary',
      isPrimary: true
    };
    
    if (contact.address) addressObj.street = contact.address;
    if (contact.address2) addressObj.street2 = contact.address2;
    if (contact.city) addressObj.city = contact.city;
    if (contact.state) addressObj.state = contact.state;
    if (contact.zipCode) addressObj.zipCode = contact.zipCode.toString();
    if (contact.county) addressObj.county = contact.county;
    if (contact.country) addressObj.country = contact.country;
    
    if (contact.coordinates?.lat && contact.coordinates?.lng) {
      addressObj.location = {
        type: 'Point',
        coordinates: [contact.coordinates.lng, contact.coordinates.lat]
      };
    }
    
    addresses.push(addressObj);
  }
  
  return addresses.length > 0 ? addresses : null;
}

// Company cache to avoid repeated database lookups
const companyCache = new Map<string, string | null>();

async function findOrCreateCompany(companyName: string, organizationId: string, createdBy: string) {
  if (!companyName?.trim()) return null;
  
  const cacheKey = `${organizationId}:${companyName.trim()}`;
  if (companyCache.has(cacheKey)) {
    return companyCache.get(cacheKey);
  }
  
  const existingCompany = await prisma.company.findFirst({
    where: {
      name: companyName.trim(),
      organizationId
    }
  });
  
  if (existingCompany) {
    companyCache.set(cacheKey, existingCompany.id);
    return existingCompany.id;
  }
  
  try {
    const newCompany = await prisma.company.create({
      data: {
        id: new ObjectId().toString(),
        name: companyName.trim(),
        organizationId,
        createdBy,
        isDeleted: false
      }
    });
    companyCache.set(cacheKey, newCompany.id);
    return newCompany.id;
  } catch (error) {
    console.error(`Error creating company ${companyName}:`, error);
    companyCache.set(cacheKey, null);
    return null;
  }
}

// Helper function to check if contact has a valid name
function hasValidName(contact: OldContact): boolean {
  const firstName = contact.firstName?.trim();
  const lastName = contact.lastName?.trim();
  
  // Contact must have at least a first name OR last name
  return !!(firstName || lastName);
}

async function migrateContactBatch(contacts: OldContact[], organizationMap: Map<string, string>) {
  const results = {
    successful: 0,
    skipped: 0,
    failed: 0,
    skippedUnnamed: 0,
    errors: [] as string[]
  };
  
  // Fallback user ID (all zeros ObjectId)
  const fallbackUserId = '000000000000000000000000';

  // Helper to extract team ID consistently
  function extractTeamId(team: any): string | null {
    if (!team) return null;
    if (typeof team === 'string') return team;
    if (typeof team === 'object' && team !== null && typeof team.$oid === 'string') {
      return team.$oid;
    }
    return team.toString();
  }

  // Helper to check if a user exists
  async function ensureUserId(userId: string | undefined | null): Promise<string> {
    if (!userId) return fallbackUserId;
    const user = await prisma.user.findUnique({ where: { id: userId } });
    if (user) return userId;
    console.warn(`User ID ${userId} not found, using fallback user ID.`);
    return fallbackUserId;
  }
  
  for (const contact of contacts) {
    try {
      // Skip contacts without valid names
      if (!hasValidName(contact)) {
        console.log(`Skipping unnamed contact ${contact._id} (firstName: "${contact.firstName}", lastName: "${contact.lastName}")`);
        results.skippedUnnamed++;
        continue;
      }

      // Extract team ID as string
      const teamId = extractTeamId(contact.team);
      const organizationId = teamId ? organizationMap.get(teamId) : null;
      if (!organizationId) {
        console.log(`Skipping contact ${contact._id} due to missing organization (team: ${JSON.stringify(contact.team)})`);
        results.skipped++;
        continue;
      }
      
      // Find or create company
      let companyId = null;
      if (contact.company && contact.createdBy) {
        companyId = await findOrCreateCompany(contact.company, organizationId, contact.createdBy);
      }
      
      // Transform data
      const phoneData = transformPhoneNumbers(contact);
      const emailData = transformEmailAddresses(contact);
      const addressData = transformAddress(contact);

      // Ensure createdBy and updatedBy exist
      const createdById = await ensureUserId(contact.createdBy || organizationId);
      const updatedById = contact.updatedBy ? await ensureUserId(contact.updatedBy) : null;

      console.log(`Upserting contact ${contact._id} (${contact.firstName || ''} ${contact.lastName || ''})`);
      const upsertedContact = await prisma.contact.upsert({
        where: { id: contact._id },
        update: {
          firstName: contact.firstName?.trim() || null,
          lastName: contact.lastName?.trim() || null,
          image: contact.profileImage || null,
          title: contact.title || null,
          persona: contact.persona || null,
          status: contact.status || null,
          address: addressData,
          phone: phoneData,
          email: emailData,
          website: contact.website || null,
          source: contact.source || null,
          stage: contact.stage || null,
          birthday: contact.birthDate ? contact.birthDate.toISOString().split('T')[0] : null,
          spouseName: contact.spouseName || null,
          summary: contact.stickyNote || null,
          companyId,
          buyerNeeds: contact.buyerNeeds?.length ? contact.buyerNeeds : null,
          organizationId,
          createdBy: createdById,
          updatedBy: updatedById,
          lastViewedAt: contact.lastViewedAt || null,
          lastViewedBy: contact.lastViewedBy || null,
          isDeleted: contact.isDeleted || contact.deleted || false,
          deletedAt: contact.deletedDate || null,
          deletedBy: contact.deletedBy || null,
          createdAt: contact.createdAt || new Date(),
          updatedAt: contact.updatedAt || new Date()
        },
        create: {
          id: contact._id,
          firstName: contact.firstName?.trim() || null,
          lastName: contact.lastName?.trim() || null,
          image: contact.profileImage || null,
          title: contact.title || null,
          persona: contact.persona || null,
          status: contact.status || null,
          address: addressData,
          phone: phoneData,
          email: emailData,
          website: contact.website || null,
          source: contact.source || null,
          stage: contact.stage || null,
          birthday: contact.birthDate ? contact.birthDate.toISOString().split('T')[0] : null,
          spouseName: contact.spouseName || null,
          summary: contact.stickyNote || null,
          companyId,
          buyerNeeds: contact.buyerNeeds?.length ? contact.buyerNeeds : null,
          organizationId,
          createdBy: createdById,
          updatedBy: updatedById,
          lastViewedAt: contact.lastViewedAt || null,
          lastViewedBy: contact.lastViewedBy || null,
          isDeleted: contact.isDeleted || contact.deleted || false,
          deletedAt: contact.deletedDate || null,
          deletedBy: contact.deletedBy || null,
          createdAt: contact.createdAt || new Date(),
          updatedAt: contact.updatedAt || new Date()
        }
      });
      console.log(`Upserted contact ${upsertedContact.id}`);
      
      // Create related contacts if any
      if (contact.relatedContacts?.length) {
        for (const relatedContact of contact.relatedContacts) {
          if (relatedContact.name || relatedContact.email || relatedContact.phone) {
            try {
              await prisma.relatedContact.create({
                data: {
                  id: new ObjectId().toString(),
                  firstName: relatedContact.name?.split(' ')[0] || null,
                  lastName: relatedContact.name?.split(' ').slice(1).join(' ') || null,
                  label: relatedContact.label || null,
                  email: relatedContact.email ? [{ address: relatedContact.email, type: 'primary' }] : null,
                  phone: relatedContact.phone ? [{ number: relatedContact.phone, type: 'primary' }] : null,
                  contactId: upsertedContact.id,
                  organizationId,
                  createdBy: createdById
                }
              });
            } catch (error) {
              console.error(`Warning: Failed to create related contact for ${contact._id}:`, error);
            }
          }
        }
      }
      
      results.successful++;
      
    } catch (error) {
      results.failed++;
      results.errors.push(`Contact ${contact._id}: ${error}`);
      console.error(`❌ Error migrating contact ${contact._id}:`, error);
    }
  }
  
  return results;
}

async function cleanupUnnamedContacts() {
  console.log('\n🧹 Cleaning up unnamed contacts...');
  
  try {
    // Find all contacts that don't have a firstName OR lastName
    const unnamedContacts = await prisma.contact.findMany({
      where: {
        AND: [
          {
            OR: [
              { firstName: null },
              { firstName: '' }
            ]
          },
          {
            OR: [
              { lastName: null },
              { lastName: '' }
            ]
          }
        ],
        isDeleted: false
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        organizationId: true
      }
    });

    console.log(`🔍 Found ${unnamedContacts.length} unnamed contacts to delete`);

    if (unnamedContacts.length === 0) {
      console.log('✅ No unnamed contacts found to clean up');
      return { deleted: 0 };
    }

    // Delete related data first (related contacts, relationships, etc.)
    const contactIds = unnamedContacts.map(c => c.id);
    
    // Delete related contacts
    const deletedRelatedContacts = await prisma.relatedContact.deleteMany({
      where: {
        contactId: { in: contactIds }
      }
    });
    console.log(`🗑️ Deleted ${deletedRelatedContacts.count} related contacts`);

    // Delete object relationships where these contacts are involved
    const deletedRelationships = await prisma.objectRelationship.updateMany({
      where: {
        OR: [
          { object1Id: { in: contactIds }, object1Type: 'contact' },
          { object2Id: { in: contactIds }, object2Type: 'contact' }
        ]
      },
      data: {
        isActive: false
      }
    });
    console.log(`🗑️ Deactivated ${deletedRelationships.count} object relationships`);

    // Delete the unnamed contacts
    const deletedContacts = await prisma.contact.deleteMany({
      where: {
        id: { in: contactIds }
      }
    });

    console.log(`✅ Deleted ${deletedContacts.count} unnamed contacts`);
    
    return { deleted: deletedContacts.count };

  } catch (error) {
    console.error('❌ Error cleaning up unnamed contacts:', error);
    return { deleted: 0 };
  }
}

async function fullMigration() {
  try {
    console.log('🚀 Starting full contact migration...');
    
    // STEP 0: Clean up existing unnamed contacts first
    const cleanupResults = await cleanupUnnamedContacts();
    
    // Connect to MongoDB
    await mongoose.connect("mongodb+srv://pbdb:<EMAIL>/propbear?retryWrites=true&w=majority");
    console.log('✅ Connected to MongoDB');
    
    // Get organization mapping - organizations preserve the old team IDs as their IDs
    const organizations = await prisma.organization.findMany({
      select: { id: true, name: true }
    });
    
    // Create proper mapping from old team ID to organization ID
    // Since we preserved the old team IDs as organization IDs, this should be direct mapping
    const organizationMap = new Map<string, string>();
    organizations.forEach(org => {
      organizationMap.set(org.id, org.id); // Direct mapping since IDs were preserved
    });
    
    console.log(`📊 Found ${organizations.length} organizations:`);
    organizations.forEach(org => {
      console.log(`  - ${org.name} (ID: ${org.id})`);
    });
    
    // STEP 1: Scan all contacts to find missing team IDs and create organizations for them
    console.log('\n🔍 Scanning contacts for missing team references...');
    
    const allowedTeamIds = ['628bff8d44cd3e01b746b737', '628ea3ebfeec685660394d1c'];
    const allowedTeamObjectIds = allowedTeamIds.map(id => new mongoose.Types.ObjectId(id));
    const contactsCursor = ContactDetails.find({ team: { $in: allowedTeamObjectIds } }).cursor();
    const missingTeamIds = new Set<string>();
    const existingTeamIds = new Set(organizationMap.keys());
    
    // Helper function to extract team ID consistently
    function extractTeamId(team: any): string | null {
      if (!team) return null;
      if (typeof team === 'string') return team;
      if (typeof team === 'object' && team !== null && typeof team.$oid === 'string') {
        return team.$oid;
      }
      return team.toString();
    }
    
    let scannedCount = 0;
    let scannedUnnamed = 0;
    for (let contact = await contactsCursor.next(); contact != null; contact = await contactsCursor.next()) {
      scannedCount++;
      
      // Track unnamed contacts in source data
      if (!hasValidName(contact)) {
        scannedUnnamed++;
      }
      
      if (scannedCount % 10000 === 0) {
        console.log(`📊 Scanned ${scannedCount.toLocaleString()} contacts (${scannedUnnamed} unnamed)...`);
      }
      
      const teamId = extractTeamId(contact.team);
      if (teamId && !existingTeamIds.has(teamId)) {
        missingTeamIds.add(teamId);
      }
    }
    
    console.log(`📊 Scanned ${scannedCount.toLocaleString()} total contacts`);
    console.log(`📊 Found ${scannedUnnamed.toLocaleString()} unnamed contacts in source data (will be skipped)`);
    console.log(`🔍 Found ${missingTeamIds.size} missing team IDs that need organizations created`);
    
    // Migrate organizations for missing teams
    if (missingTeamIds.size > 0) {
      console.log('\n🏗️ Migrating organizations for missing teams...');
      
      for (const teamId of missingTeamIds) {
        try {
          // Fetch the actual team data from MongoDB
          const oldTeam = await Team.findById(teamId);
          
          if (oldTeam) {
            const newOrg = await prisma.organization.create({
              data: {
                id: teamId, // Use the team ID as the organization ID
                name: oldTeam.name || `Team ${teamId.slice(-8)}`,
                slug: oldTeam.slug || `team-${teamId}`,
                logo: oldTeam.logo || null,
                createdAt: oldTeam.createdAt || new Date(),
                emailWatermarkEnabled: oldTeam.emailWatermarkEnabled ?? true,
              }
            });
            
            organizationMap.set(teamId, newOrg.id);
            console.log(`✅ Migrated organization: ${oldTeam.name} (ID: ${teamId})`);
          } else {
            // Fallback if team not found in MongoDB
            const newOrg = await prisma.organization.create({
              data: {
                id: teamId,
                name: `Deleted Team (${teamId.slice(-8)})`,
                slug: `deleted-team-${teamId}`,
                createdAt: new Date(),
                emailWatermarkEnabled: true,
              }
            });
            
            organizationMap.set(teamId, newOrg.id);
            console.log(`⚠️ Created fallback organization for missing team: ${teamId}`);
          }
          
        } catch (error) {
          console.error(`❌ Error migrating organization for team ${teamId}:`, error);
        }
      }
    }
    
    // Sample a few contacts to debug the team mapping
    console.log('\n🔍 Sample contact team values:');
    const sampleContacts = await ContactDetails.find({ team: { $in: allowedTeamObjectIds } }).limit(5);
    for (const contact of sampleContacts) {
      const teamId = extractTeamId(contact.team);
      const mappedOrg = organizationMap.get(teamId || '');
      const status = mappedOrg ? 'FOUND' : 'NOT FOUND';
      const hasName = hasValidName(contact) ? 'NAMED' : 'UNNAMED';
      console.log(`  - Contact ${contact._id}: team="${contact.team}" -> extracted="${teamId}" -> mapped="${mappedOrg}" (${status}) [${hasName}]`);
    }
    
    // Get total count of contacts to migrate (excluding unnamed ones)
    const totalContacts = await ContactDetails.countDocuments({
      team: { $in: allowedTeamObjectIds },
      $or: [
        { isDeleted: { $ne: true } },
        { deleted: { $ne: true } }
      ]
    });
    
    console.log(`\n📈 Total contacts in source: ${totalContacts.toLocaleString()}`);
    console.log(`📈 Estimated unnamed contacts to skip: ${scannedUnnamed.toLocaleString()}`);
    console.log(`📈 Estimated contacts to migrate: ${(totalContacts - scannedUnnamed).toLocaleString()}`);
    
    if (totalContacts === 0) {
      console.log('No contacts found to migrate.');
      return;
    }
    
    // Migration settings
    const BATCH_SIZE = 100; // Process 100 contacts at a time
    const MEMORY_BATCH_SIZE = 1000; // Fetch 1000 at a time from MongoDB
    let processed = 0;
    let successful = 0;
    let skipped = 0;
    let skippedUnnamed = 0;
    let failed = 0;
    const startTime = Date.now();
    
    console.log(`🔄 Processing in batches of ${BATCH_SIZE} (fetching ${MEMORY_BATCH_SIZE} at a time)...`);
    
    // Process contacts in batches
    let skip = 0;
    while (skip < totalContacts) {
      const memoryBatch = await ContactDetails.find({
        team: { $in: allowedTeamObjectIds },
        $or: [
          { isDeleted: { $ne: true } },
          { deleted: { $ne: true } }
        ]
      })
      .skip(skip)
      .limit(MEMORY_BATCH_SIZE)
      .lean();
      
      // Process memory batch in smaller processing batches
      for (let i = 0; i < memoryBatch.length; i += BATCH_SIZE) {
        const processingBatch = memoryBatch.slice(i, i + BATCH_SIZE) as OldContact[];
        
        const results = await migrateContactBatch(processingBatch, organizationMap);
        
        successful += results.successful;
        skipped += results.skipped;
        skippedUnnamed += results.skippedUnnamed;
        failed += results.failed;
        processed += processingBatch.length;
        
        // Progress update
        const progress = ((processed / totalContacts) * 100).toFixed(2);
        const elapsed = (Date.now() - startTime) / 1000;
        const rate = processed / elapsed;
        const eta = ((totalContacts - processed) / rate) / 60; // ETA in minutes
        
        console.log(`📊 Progress: ${progress}% (${processed.toLocaleString()}/${totalContacts.toLocaleString()}) | ✅ ${successful} | ⏭️ ${skipped} | 🚫 ${skippedUnnamed} unnamed | ❌ ${failed} | Rate: ${rate.toFixed(1)}/s | ETA: ${eta.toFixed(1)}m`);
        
        // Clear company cache periodically to prevent memory issues
        if (processed % 10000 === 0) {
          companyCache.clear();
          console.log('🧹 Cleared company cache');
        }
      }
      
      skip += MEMORY_BATCH_SIZE;
      
      // Small delay to prevent overwhelming the database
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    const totalTime = (Date.now() - startTime) / 1000;
    const avgRate = processed / totalTime;
    
    console.log('\n🎉 Migration completed!');
    console.log(`⏱️  Total time: ${(totalTime / 60).toFixed(2)} minutes`);
    console.log(`📊 Average rate: ${avgRate.toFixed(1)} contacts/second`);
    console.log(`✅ Successful: ${successful.toLocaleString()}`);
    console.log(`⏭️  Skipped (missing org): ${skipped.toLocaleString()}`);
    console.log(`🚫 Skipped (unnamed): ${skippedUnnamed.toLocaleString()}`);
    console.log(`🗑️  Cleaned up (pre-existing unnamed): ${cleanupResults.deleted.toLocaleString()}`);
    console.log(`❌ Failed: ${failed.toLocaleString()}`);
    
    if (failed > 0) {
      console.log('\n❌ Some contacts failed to migrate. Check the logs above for details.');
    }
    
    // Verify final counts
    const finalCount = await prisma.contact.count();
    const finalUnnamedCount = await prisma.contact.count({
      where: {
        AND: [
          {
            OR: [
              { firstName: null },
              { firstName: '' }
            ]
          },
          {
            OR: [
              { lastName: null },
              { lastName: '' }
            ]
          }
        ],
        isDeleted: false
      }
    });
    
    console.log(`🔍 Final contact count in database: ${finalCount.toLocaleString()}`);
    console.log(`🔍 Final unnamed contact count: ${finalUnnamedCount.toLocaleString()}`);
    
    if (finalUnnamedCount > 0) {
      console.warn(`⚠️  Warning: ${finalUnnamedCount} unnamed contacts still exist in database`);
    }
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    await prisma.$disconnect();
    console.log('🔌 Disconnected from databases');
  }
}

fullMigration(); 