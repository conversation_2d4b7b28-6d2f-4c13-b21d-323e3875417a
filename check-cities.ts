const { db } = require("./packages/database/src/client.ts");

async function checkCities() {
  try {
    console.log("=== CHECKING CITIES IN PROPERTY LOCATIONS ===");
    
    // Get a sample of unique cities to see what's actually in the database
    const cities = await db.propertyLocation.aggregateRaw({
      pipeline: [
        { $group: { _id: "$address.city", count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 20 }
      ]
    });
    
    console.log("\nTop 20 cities in PropertyLocation:");
    cities.forEach(city => {
      console.log(`- "${city._id}" (${city.count} properties)`);
    });
    
    // Check specifically for Anaheim variations
    const anaheimVariations = await db.propertyLocation.aggregateRaw({
      pipeline: [
        { $match: { "address.city": { $regex: "anaheim", $options: "i" } } },
        { $group: { _id: "$address.city", count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ]
    });
    
    console.log("\nAnaheim variations found:");
    if (anaheimVariations.length === 0) {
      console.log("- No Anaheim properties found!");
    } else {
      anaheimVariations.forEach(city => {
        console.log(`- "${city._id}" (${city.count} properties)`);
      });
    }
    
    // Let's also check a sample of what a PropertyLocation document looks like
    const sampleLocation = await db.propertyLocation.findFirst();
    console.log("\nSample PropertyLocation document:");
    console.log(JSON.stringify(sampleLocation, null, 2));
    
    await db.$disconnect();
  } catch (error) {
    console.error("Error:", error);
    await db.$disconnect();
  }
}

checkCities(); 