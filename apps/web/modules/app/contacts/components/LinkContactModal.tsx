"use client";

import React, { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import { 
  IconUser, 
  IconSearch, 
  IconArrowsLeftRight, 
  IconX,
  IconMail,
  IconPhone,
  IconSwitch3,
  IconBuilding,
  IconNotes,
  IconInfoCircle,
  IconArrowsUpDown,
  IconChevronDown,
  IconTrash,
  IconEdit,
  IconPlus,
  IconMapPin,
  IconLinkPlus,
} from "@tabler/icons-react";

import { StandardizedModal, StandardizedModalFooter } from "@ui/components/standardized-modal";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { ScrollArea } from "@ui/components/scroll-area";
import { ContactAvatar } from "@shared/components/ContactAvatar";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { cn } from "@ui/lib";

import type { ActiveOrganization } from "@repo/auth";
import { 
  useLinkContactToContact, 
  useUnlinkContactFromContact, 
  useUpdateContactRelation,
  getPrimaryEmail
} from "@shared/hooks/useLinkedContacts";
import { useSearchDebounced, type SearchResult } from "@app/search/lib/api";
import { AlertDefault } from "@ui/components/alert";
import { ComboBox } from "@ui/components/combobox";
import { PropertyAvatar } from "@shared/components/PropertyAvatar";
import { CopyableValue } from "@shared/components/CopyableValue";

interface LinkedContactData {
  id: string;
  name?: string;
  title?: string;
  avatarUrl?: string;
  image?: string;
  relation?: string;
  linkedAt?: Date;
  firstName?: string;
  lastName?: string;
  company?: {
    name?: string;
  };
  email?: Array<{
    address: string;
    isPrimary?: boolean;
  }>;
  phone?: Array<{
    number: string;
    isPrimary?: boolean;
  }>;
}

interface ContactData {
  id: string;
  firstName?: string;
  lastName?: string;
  name?: string;
  email?: string[];
  image?: string;
}

interface LinkContactModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  contact: ContactData;
  organization: ActiveOrganization;
  mode?: "add" | "edit";
  contactToEdit?: LinkedContactData;
}

const linkContactSchema = z.object({
  contactId: z.string().min(1, "Contact is required"),
  object1Role: z.string().min(1, "Your relation is required"),
  object2Role: z.string().min(1, "Their relation is required"),
});

type LinkContactFormData = z.infer<typeof linkContactSchema>;

const defaultFormValues: LinkContactFormData = {
  contactId: "",
  object1Role: "",
  object2Role: "",
};

const ContactCard = ({ 
  contact, 
  name, 
  email, 
  image, 
  onClick,
  isSelected = false,
  relationFieldName,
  relationPlaceholder,
  form,
  isSubmitting,
  showDeselect = false,
  onDeselect
}: { 
  contact?: any; 
  name: string; 
  email?: string; 
  image?: string; 
  onClick?: () => void;
  isSelected?: boolean;
  relationFieldName?: string;
  relationPlaceholder?: string;
  form?: any;
  isSubmitting?: boolean;
  showDeselect?: boolean;
  onDeselect?: () => void;
}) => {
  return (
    <div 
      className={cn(
        "w-full h-48 relative group",
        "flex flex-col border rounded-2xl aspect-square transition-all cursor-pointer",
        isSelected ? "border-blue-500" : "border-border hover:bg-muted/50",
        onClick && "cursor-pointer"
      )}
      onClick={onClick}
    >
      {/* Deselect button */}
      {showDeselect && onDeselect && (
        <Button
          variant="relio"
          size="icon"
          onClick={(e) => {
            e.stopPropagation();
            onDeselect();
          }}
          className="absolute top-2 right-2 z-10 items-center justify-center w-6 h-6 group-hover:flex hidden"
        >
          <IconX className="h-3.5 w-3.5" />
        </Button>
      )}
      
      {/* Main content */}
      <div className="flex-1 flex flex-col items-center justify-start gap-2.5 p-6 pt-10">
        {/* Avatar */}
        <div className="relative">
          <ContactAvatar
            name={name}
            avatarUrl={image}
            className="h-8 w-8"
          />
        </div>

        {/* Name */}
        <div className="text-white text-sm font-medium text-center" title={name}>
          {name}
        </div>

        {/* Email */}
        {email && (
          <div className="flex items-center gap-1.5 text-xs">
            <CopyableValue value={email} type="email" className="text-xs" />
          </div>
        )}
      </div>

      {/* Input field */}
      {relationFieldName && form ? (
        <div className="border-t border-border group focus-within:border-blue-500">
          {/* {form.formState.errors[relationFieldName] && (
            <p className="text-xs text-red-600 mt-1">
              {form.formState.errors[relationFieldName]?.message}
            </p>
          )} */}
          <Input
            placeholder={relationPlaceholder || "Enter relation..."}
            className="text-xs h-8 border-none focus-visible:ring-0"
            {...form.register(relationFieldName, {
              required: "Please specify relation",
              maxLength: { value: 64, message: "Relation is too long" },
            })}
            disabled={isSubmitting}
            autoComplete="off"
          />
        </div>
      ) : (
        <div className="py-2" />
      )}
    </div>
  );
};

const AddContactCard = ({ onClick, allContacts, form, handleContactSelect, setSearchTerm, isSubmitting }: { onClick: () => void, allContacts: ContactData[], form: any, handleContactSelect: (contact: ContactData) => void, setSearchTerm: (searchTerm: string) => void, isSubmitting: boolean }) => {
  return (
    <div 
      className="w-full h-48 flex flex-col items-center justify-center aspect-square border-2 border-dashed border-border rounded-xl hover:border-border transition-colors"
    >
      <ComboBox<ContactData>
        renderButton={() => (
          <Button variant="relio" size="sm" className="h-auto p-2 rounded-lg bg-muted/50 border border-border hover:bg-muted/70">
            <IconPlus className="h-3.5 w-3.5" />
          </Button>
        )}
        buttonClassName="!border-none !bg-transparent hover:!bg-transparent !p-0 !h-auto !w-auto !min-w-0 flex items-center justify-center"
        items={allContacts}
        value={form.watch("contactId")}
        onValueChange={(value: string) => {
          const contact = allContacts.find(c => c.id === value);
          if (contact) handleContactSelect(contact);
        }}
        onSearchChange={setSearchTerm}
        placeholder="Search and select a contact..."
        searchPlaceholder="Search contacts..."
        emptyMessage="No contacts found"
        groupLabel="Contacts"
        disabled={isSubmitting}
        className="!w-full !min-w-0"
        itemClassName="h-8"
        getSearchableText={(contact: ContactData) => {
          return `${contact.firstName || ''} ${contact.lastName || ''} ${contact.email || ''}`;
        }}
        getDisplayText={(contact: ContactData) => contact.firstName || contact.lastName || 'Untitled Contact'}
        renderItem={(contact: ContactData, isSelected: boolean) => (
          <>
            <ContactAvatar name={contact.firstName || contact.lastName || 'Contact'} avatarUrl={contact.image} className="h-5 w-5" />
            
            <div className="flex flex-col min-w-0 flex-1">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium truncate">
                  {contact.firstName || contact.lastName || 'Untitled Contact'}
                </span>
              </div>
            </div>
          </>
        )}
      />
    </div>
  );
};

const ConnectionLine = () => (
  <div className="flex items-center justify-center w-[50px]">
    <div className="flex-1 h-px bg-muted" />
    <div className="mx-1 p-0.5 bg-muted/50 border border-border rounded-md">
      <IconArrowsLeftRight className="h-3.5 w-3.5 text-gray-400" />
    </div>
    <div className="flex-1 h-px bg-muted" />
  </div>
);

export function LinkContactModal({
  open,
  onOpenChange,
  contact,
  organization,
  mode = "add",
  contactToEdit,
}: LinkContactModalProps) {
  const queryClient = useQueryClient();
  const [selectedContact, setSelectedContact] = useState<LinkedContactData | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [showContactPicker, setShowContactPicker] = useState(false);

  const form = useForm<LinkContactFormData>({
    resolver: zodResolver(linkContactSchema),
    defaultValues: defaultFormValues,
  });

  const { data: searchResponse, isLoading } = useSearchDebounced(
    searchTerm || " ",
    organization?.id || '',
    "contact",
    50,
    0,
    300
  );

  const allContacts = (searchResponse?.results || []).filter(c => c.id !== contact.id);

  useEffect(() => {
    if (mode === "edit" && contactToEdit && open) {
      setSelectedContact(contactToEdit);
      form.setValue("contactId", contactToEdit.id);
      form.setValue("object1Role", contactToEdit.relation || "");
      form.setValue("object2Role", contactToEdit.relation || "");
    }
  }, [mode, contactToEdit, open, form]);

  useEffect(() => {
    if (open && organization?.id && mode === "add" && !searchTerm) {
      setSearchTerm(" ");
    }
  }, [open, organization?.id, mode]);

  useEffect(() => { 
    if (!open) {
      setSelectedContact(null);
      setSearchTerm("");
      setShowContactPicker(false);
      form.reset(defaultFormValues);
    }
  }, [open, form]);

  const linkContactMutation = useLinkContactToContact(contact?.id || '', organization?.id || '');
  const unlinkContactMutation = useUnlinkContactFromContact(contact?.id || '', organization?.id || '');
  const updateContactRelationMutation = useUpdateContactRelation(contact?.id || '', organization?.id || '');

  const getDisplayName = (contact: any) => {
    if (contact.name) return contact.name;
    if (contact.title) return contact.title;
    if (contact.firstName || contact.lastName) {
      return [contact.firstName, contact.lastName].filter(Boolean).join(" ");
    }
    return "Unnamed person";
  };

  const getCurrentContactName = () => {
    if (contact.firstName || contact.lastName) {
      return `${contact.firstName || ''} ${contact.lastName || ''}`.trim();
    }
    return 'Unnamed person';
  };

  const handleContactSelect = (searchResult: any) => {
    const linkedContact: LinkedContactData = {
      id: searchResult.id,
      name: searchResult.name,
      title: searchResult.title,
      avatarUrl: searchResult.avatarUrl,
      firstName: searchResult.firstName,
      lastName: searchResult.lastName,
      image: searchResult.image,
      email: Array.isArray(searchResult.email) 
        ? searchResult.email 
        : typeof searchResult.email === 'string' 
          ? [{ address: searchResult.email, isPrimary: true }]
          : [],
      company: searchResult.company,
    };
    setSelectedContact(linkedContact);
    form.setValue("contactId", searchResult.id);
    setShowContactPicker(false);
  };

  const handleDeselectContact = () => {
    setSelectedContact(null);
    form.setValue("contactId", "");
    form.setValue("object1Role", "");
    form.setValue("object2Role", "");
  };

  const handleDelete = async () => {
    if (!contactToEdit || mode !== "edit") return;

    try {
      setIsSubmitting(true);
      
      await unlinkContactMutation.mutateAsync({
        relationshipId: contactToEdit.id,
      });
      
      toast.success("Contact unlinked successfully");
      onOpenChange(false);
      
      queryClient.invalidateQueries({
        queryKey: ["linkedContacts", contact.id, organization?.id],
      });
    } catch (error) {
      console.error("Error unlinking contact:", error);
      toast.error("Failed to unlink contact");
    } finally {
      setIsSubmitting(false);
    }
  };

  const onSubmit = async (formData: LinkContactFormData) => {
    if (!formData.object1Role?.trim() || !formData.object2Role?.trim()) {
      toast.error("Please specify both relations");
      return;
    }

    try {
      setIsSubmitting(true);
      
      if (mode === "edit" && contactToEdit) {
        await updateContactRelationMutation.mutateAsync({
          relationshipId: contactToEdit.id,
          object1Role: formData.object1Role.trim(),
          object2Role: formData.object2Role.trim(),
        });
        toast.success("Contact relation updated successfully");
      } else {
        if (!selectedContact) {
          toast.error("Please select a contact");
          return;
        }
        
        await linkContactMutation.mutateAsync({
          contactId: formData.contactId,
          object1Role: formData.object1Role.trim(),
          object2Role: formData.object2Role.trim(),
        });
        toast.success("Contact linked successfully");
      }

      onOpenChange(false);
      
      form.reset(defaultFormValues);
      setSelectedContact(null);
      
      queryClient.invalidateQueries({
        queryKey: ["linkedContacts", contact.id, organization?.id],
      });
    } catch (error) {
      console.error("Error with contact operation:", error);
      toast.error(mode === "edit" ? "Failed to update contact relation" : "Failed to link contact");
    } finally {
      setIsSubmitting(false);
    }
  };

  const getModalTitle = () => {
    return mode === "edit" ? "Edit Contact Relation" : "Link Contact";
  };

  const getModalDescription = () => {
    return mode === "edit" 
      ? "Update the relationship between these contacts, or remove the link entirely."
      : "Link another contact to this contact. The relationship will be established between the records.";
  };

  return (
    <StandardizedModal
      open={open}
      onOpenChange={onOpenChange}
      title={getModalTitle()}
      icon={<IconLinkPlus className="h-4 w-4" />}
      description={getModalDescription()}
      footer={
        <StandardizedModalFooter
          leftContent={
              mode === "edit" && (
                <Button
                  type="button"
                  variant="error"
                  size="sm"
                  onClick={handleDelete}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  ) : (
                    <>
                      <IconTrash className="h-4 w-4" />
                    </>
                  )}
                </Button>
              )
            }
          >
          {mode === "edit" && (
            <Button
              type="button"
              variant="error"
              onClick={handleDelete}
              disabled={isSubmitting}
            >
              <IconTrash className="h-4 w-4 mr-2" />
              Unlink
            </Button>
          )}

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="action"
            size="sm"
            disabled={isSubmitting || (mode === "add" && !selectedContact)}
            onClick={form.handleSubmit(onSubmit)}
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                <span>Link</span>
              </div>
            ) : (
              "Link"
            )}
          </Button>
        </StandardizedModalFooter>
      }
    >
      <form id="link-contact-form" onSubmit={form.handleSubmit(onSubmit)}>
        <ScrollArea className="w-full h-auto">
          <div className="mb-4">
            <AlertDefault icon={<IconInfoCircle className="h-4 w-4" />}>
              {mode === "edit" 
                ? "Update the relationship or delete the link between the contacts."
                : "Link another contact to this contact. The relationship will be established between the records."
              }
            </AlertDefault>
          </div>

          <div className="space-y-6 p-1 border rounded-2xl border-border">

            {mode === "add" && !showContactPicker && !selectedContact && (
              <div className="flex items-stretch gap-0">
                <div className="flex-1 w-full">
                  <AddContactCard 
                    onClick={() => setShowContactPicker(true)} 
                    allContacts={allContacts as ContactData[]} 
                    form={form} 
                    handleContactSelect={handleContactSelect} 
                    setSearchTerm={setSearchTerm} 
                    isSubmitting={isSubmitting} 
                  />
                </div>
                <ConnectionLine />
                <div className="flex-1 w-full">
                  <ContactCard
                    name={getCurrentContactName()}
                    email={getPrimaryEmail(contact.email)}
                    image={contact.image || undefined}
                  />
                </div>
              </div>
            )}

            {/* Selected Contact Display */}
            {(selectedContact || (mode === "edit" && contactToEdit)) && (
              <div className="flex items-stretch gap-0">
                <div className="flex-1 w-full">
                  <ContactCard
                    contact={selectedContact || contactToEdit}
                    name={getDisplayName(selectedContact || contactToEdit)}
                    email={getPrimaryEmail((selectedContact || contactToEdit)?.email)}
                    image={(selectedContact || contactToEdit)?.image || (selectedContact || contactToEdit)?.avatarUrl}
                    relationFieldName="object1Role"
                    relationPlaceholder="Relation"
                    form={form}
                    isSubmitting={isSubmitting}
                    showDeselect={mode === "add"}
                    onDeselect={handleDeselectContact}
                  />
                </div>
                <ConnectionLine />
                <div className="flex-1 w-full">
                  <ContactCard
                    name={getCurrentContactName()}
                    email={getPrimaryEmail(contact.email)}
                    image={contact.image || undefined}
                    relationFieldName="object2Role"
                    relationPlaceholder="Relation"
                    form={form}
                    isSubmitting={isSubmitting}
                  />
                </div>
              </div>
            )}

            {/* Contact Picker */}
            {showContactPicker && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Select Contact to Link</h3>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => setShowContactPicker(false)}
                  >
                    <IconX className="h-4 w-4" />
                  </Button>
                </div>
                
                <Input
                  placeholder="Search contacts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />

                                 <div className="grid grid-cols-2 gap-4 max-h-64 overflow-y-auto">
                   {allContacts.map((searchContact) => (
                     <ContactCard
                       key={searchContact.id}
                       contact={searchContact}
                       name={getDisplayName(searchContact)}
                       email={getPrimaryEmail(searchContact.email)}
                       image={searchContact.avatarUrl}
                       onClick={() => handleContactSelect(searchContact)}
                     />
                   ))}
                 </div>
              </div>
            )}


          </div>
        </ScrollArea>
      </form>
    </StandardizedModal>
  );
} 