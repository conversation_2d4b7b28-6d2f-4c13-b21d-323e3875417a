"use client";

import React, { useState } from "react";
import { useProperty, useUpdateProperty } from "@app/properties/lib/api";
import PageHeader from "@shared/components/PageHeader";
import ReorderList from "@ui/components/reorder-list";
import GridItemContainer from "@ui/components/grid-item-container";
import type { ActiveOrganization } from "@repo/auth";
import { TagSelector } from "@app/shared/components/TagSelector";
import { MoreInfo } from "@app/shared/components/MoreInfo";
import { Activities } from "@app/activities";
import { useSession } from "@app/auth/hooks/use-session";
import { useContactProperties } from "@shared/hooks/useContactProperties";
import { useLinkedContacts } from "@shared/hooks/useLinkedContacts";
import { IconId, IconLayersLinked, IconPlus } from "@tabler/icons-react";
import { useTasks } from "@app/tasks/lib/tasks-provider";
import { createNote } from "@app/notes/lib/api";
import { useQueryClient } from "@tanstack/react-query";
import {
	Ta<PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	Ta<PERSON>List,
	TabsTrigger,
} from "@ui/components/tabs";
import { Label } from "@ui/components/label";
import { PropertyView } from "./PropertyView";
import PropertyDetails from "./sections/PropertyDetails";
import { colItems } from "@app/properties/lib/columns";
import { AccessDeniedError, RecordNotFoundError } from "@shared/components/ErrorStates";
import { Loader } from "@ui/components/loader";

interface PropertyPageProps {
	id: string;
	activeOrganization: ActiveOrganization | null;
	isFavorite: boolean;
	onNavigate?: (direction: "prev" | "next") => void;
	hasPrevious?: boolean;
	hasNext?: boolean;
}

const PropertyPage = ({
	id,
	activeOrganization,
	isFavorite,
	onNavigate,
	hasPrevious,
	hasNext,
}: PropertyPageProps) => {
	const { data: property, isLoading: propertyLoading, error: propertyError } = useProperty(id, activeOrganization?.id);
	const { mutateAsync: updateProperty } = useUpdateProperty(activeOrganization?.id);
	const [isConfigureModalOpen, setIsConfigureModalOpen] = useState(false);
	const { user } = useSession();
	const { openCreateTask } = useTasks();
	const queryClient = useQueryClient();

	// Handle error states
	if (propertyError) {
		const error = propertyError as any;
		if (error.message === "ACCESS_DENIED") {
			return <AccessDeniedError message="You don't have access to view this property" />;
		}
		if (error.message === "PROPERTY_NOT_FOUND") {
			return <RecordNotFoundError recordType="property" />;
		}
	}

	// Show loading state
	if (propertyLoading) {
		return <div className="min-h-screen flex items-center justify-center !text-muted-foreground gap-2">
			<Loader text="Loading" variant="loading-dots" color="muted" />
		</div>;
	}

	if (!activeOrganization) {
		return (
			<div className="flex items-center justify-center h-screen">
				<div className="text-muted-foreground">Loading...</div>
			</div>
		);
	}

	const renderLeftColumn = (item: { id: string; title: string }) => {
		if (!activeOrganization) return null;

		switch (item.id) {
			case "details":
				return (
					<GridItemContainer item={item}>
						<PropertyDetails selectedProperty={property} organization={activeOrganization} />
					</GridItemContainer>
				);
			case "linked-records":
				return (
					<GridItemContainer item={item}>
						{/* TODO: Add linked records */}
						<span>Linked Records</span>
					</GridItemContainer>
				);
		}
	};

	return (
		<div className="flex flex-col h-[calc(100vh-50px)]">
			{/* Header */}
			<PageHeader
				activeOrganization={activeOrganization}
				data={property}
				isFavorite={isFavorite}
				objectType="property"
				onTitleUpdate={() => {}}
				onNavigate={onNavigate}
				hasPrevious={hasPrevious}
				hasNext={hasNext}
			/>

		{/* Main Content */}
		<div className="flex flex-1 overflow-hidden">
			<div className="flex-1 flex flex-col overflow-hidden">
				<Activities data={property} organization={activeOrganization} user={user} recordType="property" />
			</div>

			{/* Right Column - 1/3 width */}
			<div className="w-1/3 flex flex-col overflow-hidden border-l">
				<Tabs defaultValue="details" className="w-full flex flex-col h-full">
					{/* Sticky Tabs Header */}
					<div className="sticky top-0 z-10 border-b border-muted px-2">
						<TabsList className="w-full justify-start gap-2 h-[48px] rounded-none !bg-transparent !p-0 !border-none">
							<TabsTrigger className="gap-1 group data-[state=active]:!bg-muted/50 data-[state=active]:!text-foreground  data-[state=active]:!border-border data-[state=active]:!border border border-transparent hover:!border hover:!border-border rounded-md px-2 py-1" value="details">
								<IconId className="w-4 h-4 group-hover:text-foreground" />
								<Label className="group-hover:text-foreground">Details</Label>
							</TabsTrigger>
							<TabsTrigger className="gap-1 group data-[state=active]:!bg-muted/50 data-[state=active]:!text-foreground  data-[state=active]:!border-border data-[state=active]:!border border border-transparent hover:!border hover:!border-border rounded-md px-2 py-1" value="linked-records">
								<IconLayersLinked className="w-4 h-4 group-hover:text-foreground" />
								<Label className="group-hover:text-foreground">Linked Records</Label>
							</TabsTrigger>
						</TabsList>
					</div>

					{/* Tab Content with proper scrolling */}
					<div className="flex-1 min-h-0">
						<TabsContent value="details" className="h-full mt-0 focus-visible:outline-none ring-0 focus-visible:ring-0 focus-visible:ring-offset-0">
							<div>
							<div className="flex flex-col h-full overflow-y-auto px-2">
								<PropertyView property={property} />
							</div>
							<ReorderList
									initialItems={colItems}
									renderItem={renderLeftColumn}
									storageKey="property:details-column-order"
								/>	
							</div>
						</TabsContent>
						
						<TabsContent value="linked-records" className="px-2 h-full mt-0">
							<div className="flex items-center justify-center h-full">
								<div className="text-center text-muted-foreground">
									<div className="text-lg font-medium mb-2">Linked Records</div>
									<div className="text-sm">Linked records will be displayed here when implemented.</div>
								</div>
							</div>
						</TabsContent>
					</div>
				</Tabs>
			</div>
		</div>
		</div>
	);
};

export default PropertyPage;