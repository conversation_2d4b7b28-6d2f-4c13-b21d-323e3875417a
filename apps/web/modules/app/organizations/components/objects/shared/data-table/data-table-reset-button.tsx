"use client";

import { useDataTable } from "@app/organizations/components/objects/shared/data-table/data-table-provider";
import { useHotkeys } from "@app/shared/hooks/useHotKeys";
import { Button } from "@ui/components/button";
import { Kbd } from "@ui/components/kbd";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { X } from "lucide-react";

export function DataTableResetButton() {
	const { table } = useDataTable();
	
	// Check if there are any active column filters
	const hasActiveFilters = table.getState().columnFilters.length > 0;

	const resetFilters = () => {
		// Reset the table's column filters - DataTableInfinite should handle URL synchronization
		table.resetColumnFilters();
	};

	useHotkeys([
		{
			key: "Escape",
			callback: () => resetFilters(),
		},
	]);

	// Don't render the button if there are no active filters
	if (!hasActiveFilters) {
		return null;
	}

	return (
		<TooltipProvider>
			<Tooltip>
				<TooltipTrigger asChild>
					<Button
						variant="relio"
						size="sm"
						className="h-7"
						onClick={() => resetFilters()}
					>
						<X className="mr-2 h-4 w-4" />
						Reset
					</Button>
				</TooltipTrigger>
				<TooltipContent side="left">
					<p>
						Reset filters with{" "}
						<Kbd className="ml-1 text-muted-foreground group-hover:text-accent-foreground">
							<span className="mr-1">⌘</span>
							<span>Esc</span>
						</Kbd>
					</p>
				</TooltipContent>
			</Tooltip>
		</TooltipProvider>
	);
}
