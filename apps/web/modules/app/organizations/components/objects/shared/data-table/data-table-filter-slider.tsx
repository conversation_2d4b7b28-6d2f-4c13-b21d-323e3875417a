"use client";

import { useDataTable } from "@app/organizations/components/objects/shared/data-table/data-table-provider";
import { isArrayOfNumbers } from "@app/organizations/lib/is-array";
import { useDebounce } from "@app/shared/hooks/useDebounce";
import { InputWithAddons } from "@ui/components/input-with-addons";
import { Label } from "@ui/components/label";
import { Slider } from "@ui/components/slider";
import { useEffect, useState } from "react";
import type { DataTableSliderFilterField } from "./types";

function getFilter(filterValue: unknown) {
	return typeof filterValue === "number"
		? [filterValue, filterValue]
		: Array.isArray(filterValue) && isArrayOfNumbers(filterValue)
			? filterValue.length === 1
				? [filterValue[0], filterValue[0]]
				: filterValue
			: null;
}

// TODO: discuss if we even need the `defaultMin` and `defaultMax`
export function DataTableFilterSlider<TData>({
	value: _value,
	min: defaultMin,
	max: defaultMax,
}: DataTableSliderFilterField<TData>) {
	const value = _value as string;
	const { table, columnFilters, getFacetedMinMaxValues } = useDataTable();
	
	// Safe column lookup with fallback for server-side filtering
	let column = null;
	try {
		column = table.getColumn(value);
	} catch (error) {
		// Column doesn't exist, use server-side filtering fallback
		console.debug(`[Table] Column '${value}' not found, using server-side filtering`);
	}
	
	const filterValue = columnFilters.find((i) => i.id === value)?.value;
	const filters = getFilter(filterValue);
	const [input, setInput] = useState<number[] | null>(filters);
	const [min, max] = getFacetedMinMaxValues?.(table, value) ||
		column?.getFacetedMinMaxValues() || [defaultMin, defaultMax];

	const debouncedInput = useDebounce(input, 500);

	useEffect(() => {
		if (debouncedInput?.length === 2) {
			if (column) {
				column.setFilterValue(debouncedInput);
			} else {
				// For server-side filtering, manually set filter even if no column exists
				const currentFilters = table.getState().columnFilters;
				const newFilters = currentFilters.filter(f => f.id !== value);
				newFilters.push({ id: value, value: debouncedInput });
				table.setColumnFilters(newFilters);
			}
		}
	}, [debouncedInput, column, table, value]);

	useEffect(() => {
		if (debouncedInput?.length !== 2) {
		} else if (!filters) {
			setInput(null);
		} else if (
			debouncedInput[0] !== filters[0] ||
			debouncedInput[1] !== filters[1]
		) {
			setInput(filters);
		}
	}, [filters]);

	return (
		<div className="grid gap-2">
			<div className="flex items-center gap-4">
				<div className="grid w-full gap-1.5">
					<Label
						htmlFor={`min-${value}`}
						className="px-2 text-muted-foreground"
					>
						Min.
					</Label>
					<InputWithAddons
						placeholder="from"
						trailing="ms"
						containerClassName="mb-2 h-9 rounded-lg"
						type="number"
						name={`min-${value}`}
						id={`min-${value}`}
						value={`${input?.[0] ?? min}`}
						min={min}
						max={max}
						onChange={(e) =>
							setInput((prev) => [
								Number(e.target.value),
								prev?.[1] || max,
							])
						}
					/>
				</div>
				<div className="grid w-full gap-1.5">
					<Label
						htmlFor={`max-${value}`}
						className="px-2 text-muted-foreground"
					>
						Max.
					</Label>
					<InputWithAddons
						placeholder="to"
						trailing="ms"
						containerClassName="mb-2 h-9 rounded-lg"
						type="number"
						name={`max-${value}`}
						id={`max-${value}`}
						value={`${input?.[1] ?? max}`}
						min={min}
						max={max}
						onChange={(e) =>
							setInput((prev) => [
								prev?.[0] || min,
								Number(e.target.value),
							])
						}
					/>
				</div>
			</div>
			<Slider
				min={min}
				max={max}
				value={input?.length === 2 ? input : [min, max]}
				onValueChange={(values) => setInput(values)}
			/>
		</div>
	);
}
