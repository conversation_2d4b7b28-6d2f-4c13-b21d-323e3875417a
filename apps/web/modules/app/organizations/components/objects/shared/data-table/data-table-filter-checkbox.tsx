"use client";

import { useDataTable } from "@app/organizations/components/objects/shared/data-table/data-table-provider";
import { formatCompactNumber } from "@app/organizations/lib/format";
import { Checkbox } from "@ui/components/checkbox";
import { InputWithAddons } from "@ui/components/input-with-addons";
import { Label } from "@ui/components/label";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";
import { Search } from "lucide-react";
import { useState } from "react";
import type { DataTableCheckboxFilterField } from "./types";

export function DataTableFilterCheckbox<TData>({
	value: _value,
	options,
	component,
}: DataTableCheckboxFilterField<TData>) {
	const value = _value as string;
	const [inputValue, setInputValue] = useState("");
	const { table, columnFilters, isLoading, getFacetedUniqueValues } =
		useDataTable();
	// Safe column lookup with fallback for server-side filtering
	let column = null;
	try {
		column = table.getColumn(value);
	} catch (error) {
		// Column doesn't exist, use server-side filtering fallback
		console.debug(`[Table] Column '${value}' not found, using server-side filtering`);
	}
	
	// REMINDER: avoid using column?.getFilterValue()
	const filterValue = columnFilters.find((i) => i.id === value)?.value;
	const facetedValue =
		getFacetedUniqueValues?.(table, value) ||
		column?.getFacetedUniqueValues();

	const Component = component;

	// filter out the options based on the input value
	const filterOptions = options?.filter(
		(option) =>
			inputValue === "" ||
			option.label.toLowerCase().includes(inputValue.toLowerCase()),
	);

	// CHECK: it could be filterValue or searchValue
	const filters = filterValue
		? Array.isArray(filterValue)
			? filterValue
			: [filterValue]
		: [];

	// REMINDER: if no options are defined, while fetching data, we should show a skeleton
	if (isLoading && !filterOptions?.length)
		return (
			<div className="grid divide-y rounded-lg border border-border">
				{Array.from({ length: 3 }).map((_, index) => (
					<div
						key={index}
						className="flex items-center justify-between gap-2 px-2 py-2.5"
					>
						<Skeleton className="h-4 w-4 rounded-sm" />
						<Skeleton className="h-4 w-full rounded-sm" />
					</div>
				))}
			</div>
		);

	if (!filterOptions?.length) return null;

	return (
		<div className="grid gap-2 cursor-pointer">
			{options && options.length > 4 ? (
				<InputWithAddons
					placeholder="Search"
					leading={<Search className="mt-0.5 h-4 w-4" />}
					containerClassName="h-9 rounded-lg bg-muted/50"
					className="bg-muted/50"
					value={inputValue}
					onChange={(e) => setInputValue(e.target.value)}
				/>
			) : null}
			{/* FIXME: due to the added max-h and overflow-y-auto, the hover state and border is laying on top of the scroll bar */}
			<div className="max-h-[200px] overflow-y-auto rounded-lg border border-border empty:border-none">
				{filterOptions.map((option, index) => {
					const checked = filters.includes(option.value);
					const optionKey = `${value}-${option.value}-${index}`;

					return (
						<div
							key={optionKey}
							className={cn(
								"group relative flex items-center space-x-2 px-2 py-2.5 hover:bg-accent/50",
								index !== filterOptions.length - 1
									? "border-b"
									: undefined,
							)}
						>
							<Checkbox
								id={optionKey}
								checked={checked}
								className="cursor-pointer"
								onCheckedChange={(checked) => {
									const newValue = checked
										? [...(filters || []), option.value]
										: filters?.filter(
												(value) =>
													option.value !== value,
											);
									
									const filterValue = newValue?.length ? newValue : undefined;
									
									if (column) {
										column.setFilterValue(filterValue);
									} else {
										// For server-side filtering, manually set filter even if no column exists
										const currentFilters = table.getState().columnFilters;
										const newFilters = currentFilters.filter(f => f.id !== value);
										if (filterValue !== undefined) {
											newFilters.push({ id: value, value: filterValue });
										}
										table.setColumnFilters(newFilters);
									}
								}}
							/>
							<Label
								htmlFor={optionKey}
								className="flex w-full items-center justify-center gap-1 truncate text-foreground/70 group-hover:text-accent-foreground cursor-pointer"
							>
								{Component ? (
									<Component {...option} />
								) : (
									<span className="truncate font-normal">
										{option.label}
									</span>
								)}
								<span className="ml-auto flex items-center justify-center font-mono text-xs">
									{isLoading ? (
										<Skeleton className="h-4 w-4" />
									) : facetedValue?.has(option.value) ? (
										formatCompactNumber(
											facetedValue.get(
												option.value,
											) || 0,
										)
									) : null}
								</span>
								<button
									type="button"
									onClick={() => {
										const filterValue = [option.value];
										
										if (column) {
											column.setFilterValue(filterValue);
										} else {
											// For server-side filtering, manually set filter even if no column exists
											const currentFilters = table.getState().columnFilters;
											const newFilters = currentFilters.filter(f => f.id !== value);
											newFilters.push({ id: value, value: filterValue });
											table.setColumnFilters(newFilters);
										}
									}}
									className={cn(
										"cursor-pointer",
										"absolute inset-y-0 right-0 hidden font-normal text-muted-foreground backdrop-blur-sm hover:text-foreground group-hover:block",
										"rounded-md ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
									)}
								>
									<span className="px-2">only</span>
								</button>
							</Label>
						</div>
					);
				})}
			</div>
		</div>
	);
}
