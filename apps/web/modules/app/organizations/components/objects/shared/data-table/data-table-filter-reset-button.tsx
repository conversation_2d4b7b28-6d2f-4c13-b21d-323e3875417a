"use client";

import { useDataTable } from "@app/organizations/components/objects/shared/data-table/data-table-provider";
import { Button } from "@ui/components/button";
import { X } from "lucide-react";
import type { DataTableFilterField } from "./types";

export function DataTableFilterResetButton<TData>({
	value: _value,
}: DataTableFilterField<TData>) {
	const { columnFilters, table } = useDataTable();
	const value = _value as string;

	// Early return if table is not initialized
	if (!table) return null;

	// Safe column lookup with fallback for server-side filtering
	let column = null;
	try {
		column = table.getColumn(value);
	} catch (error) {
		// Column doesn't exist, but we can still handle server-side filtering
		console.debug(`[Table] Column '${value}' not found in table, using fallback for server-side filtering`);
	}

	const filterValue = columnFilters.find((f) => f.id === value)?.value;

	// TODO: check if we could useMemo
	const filters = filterValue
		? Array.isArray(filterValue)
			? filterValue
			: [filterValue]
		: [];

	// If no filters, don't render
	if (filters.length === 0) return null;

	return (
		<Button
			variant="outline"
			className="h-5 rounded-full px-1.5 py-1 font-mono text-[10px]"
			onClick={(e) => {
				e.stopPropagation();
				// For server-side filtering, we need to clear the filter even if no column exists
				if (column) {
					column.setFilterValue(undefined);
				} else {
					// Manually trigger filter change for server-side filtering
					const currentFilters = table.getState().columnFilters;
					const newFilters = currentFilters.filter(f => f.id !== value);
					table.setColumnFilters(newFilters);
				}
			}}
			onKeyDown={(e) => {
				e.stopPropagation();
				if (e.code === "Enter") {
					if (column) {
						column.setFilterValue(undefined);
					} else {
						// Manually trigger filter change for server-side filtering
						const currentFilters = table.getState().columnFilters;
						const newFilters = currentFilters.filter(f => f.id !== value);
						table.setColumnFilters(newFilters);
					}
				}
			}}
			asChild
		>
			{/* REMINDER: `AccordionTrigger` is also a button(!) and we get Hydration error when rendering button within button */}
			<div role="button" tabIndex={0}>
				<span>{filters.length}</span>
				<X className="ml-1 h-2.5 w-2.5 text-muted-foreground" />
			</div>
		</Button>
	);
}
