"use client";

import {
	IconBath,
	IconBed,
	IconCalendar,
	IconCurrencyDollar,
	IconDoor,
	IconHome,
	IconMapPin,
	IconRuler,
	IconTag,
	IconTags,
} from "@tabler/icons-react";
import type { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@ui/components/badge";
import { Checkbox } from "@ui/components/checkbox";
import { DataTableColumnTags } from "../shared/data-table/data-table-column/data-table-column-tags";
import { cn } from "@ui/lib";
import type { Property } from "./schema";

export const columns: ColumnDef<Property>[] = [
	{
		id: "select",
		header: ({ table }) => (
			<Checkbox
				checked={table.getIsAllPageRowsSelected()}
				onCheckedChange={(value) =>
					table.toggleAllPageRowsSelected(!!value)
				}
				aria-label="Select all"
				className="translate-y-[2px]"
			/>
		),
		cell: ({ row }) => (
			<Checkbox
				checked={row.getIsSelected()}
				onCheckedChange={(value) => row.toggleSelected(!!value)}
				aria-label="Select row"
				className="translate-y-[2px]"
			/>
		),
		enableSorting: false,
		enableHiding: false,
	},
	{
		accessorKey: "name",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconHome className="h-4 w-4" />
				Property Name
			</div>
		),
		cell: ({ row }) => {
			const name = row.getValue("name") as string;
			return (
				<div className="w-[200px]">
					{name || <span className="text-muted-foreground">—</span>}
				</div>
			);
		},
		size: 200,
	},
	{
		accessorKey: "address",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconMapPin className="h-4 w-4" />
				Address
			</div>
		),
		cell: ({ row }) => {
			const address = row.original.address;
			if (!address)
				return <span className="text-muted-foreground">—</span>;

			const addressParts = [
				address.street,
				address.city,
				address.state,
			].filter(Boolean);

			const fullAddress = addressParts.join(", ");

			return (
				<div className="max-w-[200px] truncate" title={fullAddress}>
					{fullAddress || "—"}
				</div>
			);
		},
		enableSorting: true,
		size: 200,
	},
	{
		id: "address.street",
		accessorFn: (row) => row.address?.street || "",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconMapPin className="h-4 w-4" />
				Street
			</div>
		),
		cell: ({ row }) => {
			const street = row.original.address?.street;
			return (
				<div className="max-w-[150px] truncate">
					{street || <span className="text-muted-foreground">—</span>}
				</div>
			);
		},
		enableSorting: true,
		size: 150,
	},
	{
		id: "address.city",
		accessorFn: (row) => row.address?.city || "",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconMapPin className="h-4 w-4" />
				City
			</div>
		),
		cell: ({ row }) => {
			const city = row.original.address?.city;
			return (
				<div className="max-w-[120px] truncate">
					{city || <span className="text-muted-foreground">—</span>}
				</div>
			);
		},
		enableSorting: true,
		size: 120,
	},
	{
		id: "address.state",
		accessorFn: (row) => row.address?.state || "",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconMapPin className="h-4 w-4" />
				State
			</div>
		),
		cell: ({ row }) => {
			const state = row.original.address?.state;
			return (
				<div className="max-w-[80px] truncate">
					{state || <span className="text-muted-foreground">—</span>}
				</div>
			);
		},
		enableSorting: true,
		size: 80,
	},
	{
		id: "address.zip",
		accessorFn: (row) => row.address?.zip || "",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconMapPin className="h-4 w-4" />
				Zip
			</div>
		),
		cell: ({ row }) => {
			const zip = row.original.address?.zip;
			return (
				<div className="max-w-[80px] truncate">
					{zip || <span className="text-muted-foreground">—</span>}
				</div>
			);
		},
		enableSorting: true,
		size: 80,
	},
	{
		accessorKey: "propertyType",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconTags className="h-4 w-4" />
				Type
			</div>
		),
		cell: ({ row }) => {
			const type = row.getValue("propertyType") as string;
			if (!type) return <span className="text-muted-foreground">—</span>;

			return (
				<Badge className="capitalize bg-blue-100 text-blue-800">
					{type.replace("_", " ")}
				</Badge>
			);
		},
		filterFn: (row, id, value) => {
			return Array.isArray(value)
				? value.includes(row.getValue(id))
				: true;
		},
		enableSorting: true,
		size: 120,
	},
	{
		accessorKey: "status",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconTags className="h-4 w-4" />
				Status
			</div>
		),
		cell: ({ row }) => {
			const status = row.getValue("status") as string;
			if (!status)
				return <span className="text-muted-foreground">—</span>;

			const statusColors: Record<string, string> = {};

			const colorClass =
				statusColors[status.toLowerCase()] ||
				"bg-gray-100 text-gray-800";

			return (
				<Badge className={`capitalize ${colorClass}`}>
					{status.replace("_", " ")}
				</Badge>
			);
		},
		filterFn: (row, id, value) => {
			return Array.isArray(value)
				? value.includes(row.getValue(id))
				: true;
		},
		enableSorting: true,
		size: 120,
	},
	{
		accessorKey: "price",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconCurrencyDollar className="h-4 w-4" />
				Price
			</div>
		),
		cell: ({ row }) => {
			const price = row.getValue("price") as number;
			if (!price) return <span className="text-muted-foreground">—</span>;

			return new Intl.NumberFormat("en-US", {
				style: "currency",
				currency: "USD",
				minimumFractionDigits: 0,
			}).format(price);
		},
		enableSorting: true,
		size: 120,
	},
	{
		accessorKey: "bedrooms",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconBed className="h-4 w-4" />
				Bed
			</div>
		),
		cell: ({ row }) => {
			const bedrooms = row.getValue("bedrooms") as number;
			return bedrooms ? (
				`${bedrooms} bed`
			) : (
				<span className="text-muted-foreground">—</span>
			);
		},
		enableSorting: true,
		size: 80,
	},
	{
		accessorKey: "bathrooms",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconBath className="h-4 w-4" />
				Bath
			</div>
		),
		cell: ({ row }) => {
			const bathrooms = row.getValue("bathrooms") as number;
			return bathrooms ? (
				`${bathrooms} bath`
			) : (
				<span className="text-muted-foreground">—</span>
			);
		},
		enableSorting: true,
		size: 80,
	},
	{
		accessorKey: "squareFootage",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconRuler className="h-4 w-4" />
				Sq Ft
			</div>
		),
		cell: ({ row }) => {
			const sqft = row.getValue("squareFootage") as number;
			if (!sqft) return <span className="text-muted-foreground">—</span>;

			return new Intl.NumberFormat("en-US").format(sqft);
		},
		enableSorting: true,
		size: 100,
	},
	{
		accessorKey: "yearBuilt",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconCalendar className="h-4 w-4" />
				Year Built
			</div>
		),
		cell: ({ row }) => {
			const yearBuilt = row.getValue("yearBuilt") as number;
			return yearBuilt ? (
				yearBuilt.toString()
			) : (
				<span className="text-muted-foreground">—</span>
			);
		},
		enableSorting: true,
		size: 100,
	},
	{
		id: "units",
		accessorKey: "units",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconDoor className="h-4 w-4" />
				Units
			</div>
		),
		cell: ({ row }) => {
			const units = row.getValue("units") as number;
			return units ? (
				units.toString()
			) : (
				<span className="text-muted-foreground">—</span>
			);
		},
		enableSorting: true,
		enableColumnFilter: true,
		filterFn: "inNumberRange",
		size: 80,
	},
	{
		accessorKey: "mlsStatus",
		header: "MLS Status",
		cell: ({ row }) => {
			const mlsStatus = row.getValue("mlsStatus") as string;
			if (!mlsStatus)
				return <span className="text-muted-foreground">—</span>;

			const statusColors: Record<string, string> = {
				active: "bg-green-100 text-green-800",
				pending: "bg-yellow-100 text-yellow-800",
				sold: "bg-blue-100 text-blue-800",
				cancelled: "bg-red-100 text-red-800",
				expired: "bg-gray-100 text-gray-800",
			};

			const colorClass =
				statusColors[mlsStatus.toLowerCase()] ||
				"bg-gray-100 text-gray-800";

			return (
				<Badge className={`capitalize ${colorClass}`}>
					{mlsStatus.replace("_", " ")}
				</Badge>
			);
		},
		filterFn: (row, id, value) => {
			return Array.isArray(value)
				? value.includes(row.getValue(id))
				: true;
		},
		enableSorting: true,
		size: 120,
	},
	{
		id: "tags",
		accessorFn: (row) => [],
		header: ({ column }) => (
			<div className="items-center flex gap-1 text-primary">
				<IconTag className="h-4 w-4" />
				Tags
			</div>
		),
		// No custom cell renderer - will use InlineCellEditor with fieldType="tags"
		size: 200,
		minSize: 150,
		maxSize: 400,
	},
];
