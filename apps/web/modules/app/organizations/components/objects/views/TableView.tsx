"use client";

import { useMemo } from "react";
import { ObjectType } from "@repo/database";
import { companyConfig } from "../companies/config";
import { contactConfig } from "../contacts/config";
import { ObjectsHeader } from "../ObjectsHeader";
import { propertiesConfig } from "../properties/config";
import { createDynamicColumns } from "../shared/DynamicColumnFactory";
import { createDynamicFilterFields } from "../shared/DynamicFilterFactory";
import { DataTablePaginated } from "../shared/DataTablePaginated";

interface TableViewProps {
	objectType: ObjectType;
	organizationId: string;
	view?: {
		id: string;
		name: string;
		columnDefs: Array<{
			field: string;
			headerName: string;
			width: number;
			type?: string;
		}>;
		filters?: Array<{
			field: string;
			logic: string;
			text?: string;
			number?: number | number[];
		}>;
	};
	user?: {
		name: string;
		avatarUrl?: string | null;
	};
	views?: any[];
	onViewChange?: (viewId: string) => void;
	onRenameView?: (viewId: string) => void;
	onDuplicateView?: (viewId: string) => void;
	onDeleteView?: (viewId: string) => void;
	onToggleViewFavorite?: (viewId: string) => void;
	onToggleViewPublic?: (viewId: string) => void;
	onSetViewDefault?: (viewId: string) => void;
	onViewRenamed?: (updatedView: any) => void;
}

// Configuration mapping for object types
type ConfigMapType = {
	[K in ObjectType]: {
		config: any;
	};
};

const EMPTY_CONFIG = {};

const CONFIG_MAP: ConfigMapType = {
	contact: { config: contactConfig },
	company: { config: companyConfig },
	property: { config: propertiesConfig },
	custom_object: { config: EMPTY_CONFIG },
	task: { config: EMPTY_CONFIG },
	note: { config: EMPTY_CONFIG },
	view: { config: EMPTY_CONFIG },
};

function DynamicTable({
	objectType,
	organizationId,
	renderHeader,
	view,
	onViewRenamed,
}: {
	objectType: ObjectType;
	organizationId: string;
	renderHeader: () => React.ReactNode;
	view?: any;
	onViewRenamed?: (updatedView: any) => void;
}) {
	const { config } = CONFIG_MAP[objectType];

	const dynamicConfig = useMemo(() => {
		if (view?.columnDefs && view.columnDefs.length > 0) {
			const dynamicColumns = createDynamicColumns(
				view.columnDefs,
				objectType,
			) as any;
			const availableColumnIds = new Set(
				dynamicColumns.map((col: any) => col.id),
			);

			const existingFilterFields = config.filterFields.filter(
				(field: any) => availableColumnIds.has(field.value as string),
			);

			const existingFilterFieldIds: Set<string> = new Set(
				existingFilterFields.map((field: any) => field.value as string),
			);
			const newFilterFields = createDynamicFilterFields(
				Array.from(availableColumnIds) as string[],
				view.columnDefs,
				objectType,
				existingFilterFieldIds
			);

			const allFilterFields = [
				...existingFilterFields,
				...newFilterFields,
			];

			return {
				...config,
				columns: dynamicColumns,
				filterFields: allFilterFields,
			};
		}
		return config;
	}, [view?.columnDefs, view?.id, view?.updatedAt, config, objectType]);

	return (
		<DataTablePaginated
			organizationId={organizationId}
			config={dynamicConfig}
			renderHeader={renderHeader}
			view={view}
			onUpdateView={onViewRenamed}
		/>
	);
}

export function TableView({
	objectType,
	organizationId,
	view,
	user,
	views = [],
	onViewChange,
	onRenameView,
	onDuplicateView,
	onDeleteView,
	onToggleViewFavorite,
	onToggleViewPublic,
	onSetViewDefault,
	onViewRenamed,
}: TableViewProps) {
	const handleCreateNew = () => {
		// TODO: Implement object creation
		console.warn(`Create new ${objectType.slice(0, -1)}`);
	};

	if (!organizationId) {
		return (
			<div className="flex items-center justify-center h-64">
				<div className="text-center">
					<p className="text-muted-foreground">
						Organization not found...
					</p>
				</div>
			</div>
		);
	}

	const { config } = CONFIG_MAP[objectType];

	const renderHeader = () => (
		<ObjectsHeader
			objectType={objectType}
			organizationId={organizationId}
			view={view}
			views={views}
			onViewChange={onViewChange}
			onViewRenamed={onViewRenamed}
			onUpdateView={onViewRenamed}
			user={user}
			handleCreateNew={handleCreateNew}
			primaryColumn={config.primaryColumn}
		/>
	);

	return (
		<div className="h-full">
			<DynamicTable
				objectType={objectType}
				organizationId={organizationId}
				renderHeader={renderHeader}
				view={view}
				onViewRenamed={onViewRenamed}
			/>
		</div>
	);
}
