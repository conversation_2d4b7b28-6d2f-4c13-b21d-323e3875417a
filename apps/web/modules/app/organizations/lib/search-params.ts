import {
	createParser,
	createSearchParamsCache,
	createSerializer,
	type inferParserType,
	parseAsArrayOf,
	parseAsBoolean,
	parseAsInteger,
	parseAsString,
	parseAsStringLiteral,
	parseAsTimestamp,
} from "nuqs/server";
import {
	ARRAY_DELIMITER,
	parseAsFlexibleTimestamp,
	RANGE_DELIMITER,
	SLIDER_DELIMITER,
	SORT_DELIMITER,
} from "@app/organizations/lib/search-params-factory";
import { CONTACT_PERSONA, CONTACT_STAGE, CONTACT_STATUS } from "@app/shared/lib/constants";

export const parseAsSort = createParser({
	parse(queryValue) {
		const [id, desc] = queryValue.split(SORT_DELIMITER);
		if (!id && !desc) return null;
		return { id, desc: desc === "desc" };
	},
	serialize(value) {
		return `${value.id}.${value.desc ? "desc" : "asc"}`;
	},
});

export const searchParamsParser = {
	// FILTERS - Contact fields
	name: parseAsString,
	firstName: parseAsString,
	lastName: parseAsString,
	email: parseAsString,
	phone: parseAsString,
	website: parseAsString,
	linkedin: parseAsString,
	summary: parseAsString,
	source: parseAsString,
	title: parseAsString,
	company: parseAsString,
	status: parseAsArrayOf(
		parseAsStringLiteral(CONTACT_STATUS.map((status) => status.value)),
		ARRAY_DELIMITER,
	),
	persona: parseAsArrayOf(
		parseAsStringLiteral(CONTACT_PERSONA.map((persona) => persona.value)),
		ARRAY_DELIMITER,
	),
	stage: parseAsArrayOf(
		parseAsStringLiteral(CONTACT_STAGE.map((stage) => stage.value)),
		ARRAY_DELIMITER,
	),
	tags: parseAsArrayOf(parseAsString, ARRAY_DELIMITER),
	
	// Company filters  
	industry: parseAsArrayOf(parseAsString, ARRAY_DELIMITER),
	size: parseAsArrayOf(parseAsString, ARRAY_DELIMITER),
	description: parseAsString,
	
	// Property filters
	propertyType: parseAsArrayOf(parseAsString, ARRAY_DELIMITER),
	propertySubType: parseAsArrayOf(parseAsString, ARRAY_DELIMITER),
	market: parseAsString,
	subMarket: parseAsString,
	listingId: parseAsString,
	
	// Address fields
	city: parseAsString,
	state: parseAsString,
	zip: parseAsString,
	street: parseAsString,
	
	// Numeric fields for properties
	price: parseAsString, // Using string to handle ranges like "100000,500000"
	bedrooms: parseAsString,
	bathrooms: parseAsString,
	squareFootage: parseAsString,
	yearBuilt: parseAsString,
	lotSize: parseAsString,
	units: parseAsString,
	
	// Date range filter
	createdAt: parseAsArrayOf(parseAsFlexibleTimestamp, RANGE_DELIMITER),

	// REQUIRED FOR SORTING & PAGINATION
	sort: parseAsSort,
	limit: parseAsInteger.withDefault(100),
	start: parseAsInteger.withDefault(0),

	// REQUIRED FOR INFINITE SCROLLING
	direction: parseAsStringLiteral(["prev", "next"]).withDefault("next"),
	cursor: parseAsTimestamp.withDefault(new Date()),
	live: parseAsBoolean.withDefault(false),

	// REQUIRED FOR SELECTION
	id: parseAsString,
};

export const searchParamsCache = createSearchParamsCache(searchParamsParser);
export const searchParamsSerializer = createSerializer(searchParamsParser);

export type SearchParamsType = inferParserType<typeof searchParamsParser>;

export {
	ARRAY_DELIMITER,
	RANGE_DELIMITER,
	SLIDER_DELIMITER,
	SORT_DELIMITER,
};
