{"rustc": 12610991425282158916, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 3033921117576893, "path": 5426513424076302615, "deps": [[2671782512663819132, "tauri_utils", false, 12430904411737448031], [4899080583175475170, "semver", false, 6580408667314725885], [6913375703034175521, "schemars", false, 15766499735483826312], [7170110829644101142, "json_patch", false, 1175282956974421060], [9689903380558560274, "serde", false, 1403690401791171604], [12714016054753183456, "tauri_winres", false, 4781383065727786386], [13077543566650298139, "heck", false, 12619621859430703986], [13475171727366188400, "cargo_toml", false, 13705874330061511764], [13625485746686963219, "anyhow", false, 1360259769696615877], [15367738274754116744, "serde_json", false, 16130678139054621626], [15609422047640926750, "toml", false, 4089178403664472286], [15622660310229662834, "walkdir", false, 10227713011051960353], [16928111194414003569, "dirs", false, 10571653802019703233], [17155886227862585100, "glob", false, 6992076345437267531]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-build-4da3a7c62121253e/dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}