{"rustc": 12610991425282158916, "features": "[\"NSAttributeDescription\", \"NSEntityDescription\", \"NSFetchRequest\", \"NSManagedObjectContext\", \"NSManagedObjectModel\", \"NSPersistentStoreRequest\", \"NSPropertyDescription\", \"bitflags\"]", "declared_features": "[\"CloudKit\", \"CoreDataDefines\", \"CoreDataErrors\", \"NSAtomicStore\", \"NSAtomicStoreCacheNode\", \"NSAttributeDescription\", \"NSBatchDeleteRequest\", \"NSBatchInsertRequest\", \"NSBatchUpdateRequest\", \"NSCompositeAttributeDescription\", \"NSCoreDataCoreSpotlightDelegate\", \"NSCustomMigrationStage\", \"NSDerivedAttributeDescription\", \"NSEntityDescription\", \"NSEntityMapping\", \"NSEntityMigrationPolicy\", \"NSExpressionDescription\", \"NSFetchIndexDescription\", \"NSFetchIndexElementDescription\", \"NSFetchRequest\", \"NSFetchRequestExpression\", \"NSFetchedPropertyDescription\", \"NSFetchedResultsController\", \"NSIncrementalStore\", \"NSIncrementalStoreNode\", \"NSLightweightMigrationStage\", \"NSManagedObject\", \"NSManagedObjectContext\", \"NSManagedObjectID\", \"NSManagedObjectModel\", \"NSManagedObjectModelReference\", \"NSMappingModel\", \"NSMergePolicy\", \"NSMigrationManager\", \"NSMigrationStage\", \"NSPersistentCloudKitContainer\", \"NSPersistentCloudKitContainerEvent\", \"NSPersistentCloudKitContainerEventRequest\", \"NSPersistentCloudKitContainerOptions\", \"NSPersistentContainer\", \"NSPersistentHistoryChange\", \"NSPersistentHistoryChangeRequest\", \"NSPersistentHistoryToken\", \"NSPersistentHistoryTransaction\", \"NSPersistentStore\", \"NSPersistentStoreCoordinator\", \"NSPersistentStoreDescription\", \"NSPersistentStoreRequest\", \"NSPersistentStoreResult\", \"NSPropertyDescription\", \"NSPropertyMapping\", \"NSQueryGenerationToken\", \"NSRelationshipDescription\", \"NSSaveChangesRequest\", \"NSStagedMigrationManager\", \"alloc\", \"bitflags\", \"block2\", \"default\", \"gnustep-1-7\", \"gnustep-1-8\", \"gnustep-1-9\", \"gnustep-2-0\", \"gnustep-2-1\", \"objc2-cloud-kit\", \"std\"]", "target": 7515372154814490868, "profile": 8196097686603091492, "path": 7680036111237572435, "deps": [[1386409696764982933, "objc2", false, 13381493469831469129], [7896293946984509699, "bitflags", false, 14968080525071963818], [9859211262912517217, "objc2_foundation", false, 17239405890284809751]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/objc2-core-data-a1af40f415c031d1/dep-lib-objc2_core_data", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}