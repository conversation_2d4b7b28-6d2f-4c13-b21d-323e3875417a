{"rustc": 12610991425282158916, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 3033921117576893, "path": 12467265937495298684, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 13760224378005156377], [3060637413840920116, "proc_macro2", false, 12414406020429461685], [3150220818285335163, "url", false, 15520106702350090180], [3191507132440681679, "serde_untagged", false, 1256085761506072575], [4071963112282141418, "serde_with", false, 17269925200606108089], [4899080583175475170, "semver", false, 6580408667314725885], [5986029879202738730, "log", false, 9920811875407412337], [6606131838865521726, "ctor", false, 9973762691008557868], [6913375703034175521, "schemars", false, 15766499735483826312], [7170110829644101142, "json_patch", false, 1175282956974421060], [8319709847752024821, "uuid", false, 603275615056532074], [9010263965687315507, "http", false, 13519363488636694896], [9451456094439810778, "regex", false, 1578284039987313505], [9556762810601084293, "brotli", false, 9965213716489469934], [9689903380558560274, "serde", false, 1403690401791171604], [10806645703491011684, "thiserror", false, 591031321967214246], [11655476559277113544, "cargo_metadata", false, 9567707924673790182], [11989259058781683633, "dunce", false, 4762664516403991501], [13625485746686963219, "anyhow", false, 1360259769696615877], [14232843520438415263, "html5ever", false, 2029128165960822960], [14885200901422974105, "swift_rs", false, 13084965947726380878], [15088007382495681292, "kuchiki", false, 17872438582127952846], [15367738274754116744, "serde_json", false, 16130678139054621626], [15609422047640926750, "toml", false, 4089178403664472286], [15622660310229662834, "walkdir", false, 10227713011051960353], [15932120279885307830, "memchr", false, 12656516855636508274], [17146114186171651583, "infer", false, 16398747846922022457], [17155886227862585100, "glob", false, 6992076345437267531], [17186037756130803222, "phf", false, 200872946394218822], [17990358020177143287, "quote", false, 3342190915752399231]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-utils-d5d0cf7970f5fc86/dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}