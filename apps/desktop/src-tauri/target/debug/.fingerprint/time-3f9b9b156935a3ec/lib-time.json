{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"formatting\", \"local-offset\", \"macros\", \"parsing\", \"serde\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"formatting\", \"large-dates\", \"local-offset\", \"macros\", \"parsing\", \"quickcheck\", \"rand\", \"serde\", \"serde-human-readable\", \"serde-well-known\", \"std\", \"wasm-bindgen\"]", "target": 3713843603798095488, "profile": 17255934022493190207, "path": 5280690051634207967, "deps": [[253581978874359338, "deranged", false, 14322419023988476742], [724804171976944018, "num_conv", false, 3434866573179117697], [1509944293013079861, "time_macros", false, 632396179989433765], [4684437522915235464, "libc", false, 2802231048861149951], [4880290578780516359, "num_threads", false, 16608511372064997529], [5901133744777009488, "powerfmt", false, 7796809259583340801], [7695812897323945497, "itoa", false, 12918509813637510825], [9689903380558560274, "serde", false, 8746395647640681899], [9886904983647127192, "time_core", false, 1274319388423287623]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/time-3f9b9b156935a3ec/dep-lib-time", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}