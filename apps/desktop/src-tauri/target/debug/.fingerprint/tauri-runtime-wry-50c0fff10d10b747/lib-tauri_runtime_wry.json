{"rustc": 12610991425282158916, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 5347358027863023418, "path": 14739426990422067156, "deps": [[1386409696764982933, "objc2", false, 13381493469831469129], [2671782512663819132, "tauri_utils", false, 7377670638624321469], [3150220818285335163, "url", false, 6545121298977936358], [4143744114649553716, "raw_window_handle", false, 17963306475012769695], [5986029879202738730, "log", false, 11012419202697596293], [6089812615193535349, "tauri_runtime", false, 1230350147409879120], [8826339825490770380, "tao", false, 14988717454815988002], [9010263965687315507, "http", false, 13519363488636694896], [9141053277961803901, "wry", false, 2920334110563211858], [9859211262912517217, "objc2_foundation", false, 17239405890284809751], [10575598148575346675, "objc2_app_kit", false, 3478623338277614586], [11599800339996261026, "build_script_build", false, 10481378347860708222]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-wry-50c0fff10d10b747/dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}