{"rustc": 12610991425282158916, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 3033************, "path": 13992297986477871251, "deps": [[2671782512663819132, "tauri_utils", false, 12430904411737448031], [3060637413840920116, "proc_macro2", false, 12414406020429461685], [3150220818285335163, "url", false, 15520106702350090180], [4899080583175475170, "semver", false, 6580408667314725885], [4974441333307933176, "syn", false, 9352464358819218861], [7170110829644101142, "json_patch", false, 1175282956974421060], [7392050791754369441, "ico", false, 9600457631481963495], [8319709847752024821, "uuid", false, 603275615056532074], [9556762810601084293, "brotli", false, 9965213716489469934], [9689903380558560274, "serde", false, 1403690401791171604], [9857275760291862238, "sha2", false, 5793487205759323713], [10806645703491011684, "thiserror", false, 591031321967214246], [12409575957772518135, "time", false, 9206418660607993530], [12687914511023397207, "png", false, 9398957209685196349], [13077212702700853852, "base64", false, 8017423118161671007], [14687315689416489882, "plist", false, 16229304125869239157], [15367738274754116744, "serde_json", false, 16130678139054621626], [15622660310229662834, "walkdir", false, 10227713011051960353], [17990358020177143287, "quote", false, 3342190915752399231]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-codegen-2ca9b55ed4d9374f/dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}