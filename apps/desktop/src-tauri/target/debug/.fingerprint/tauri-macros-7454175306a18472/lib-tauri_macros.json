{"rustc": 12610991425282158916, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 3033921117576893, "path": 6805413809821779974, "deps": [[2671782512663819132, "tauri_utils", false, 12430904411737448031], [3060637413840920116, "proc_macro2", false, 12414406020429461685], [4974441333307933176, "syn", false, 9352464358819218861], [13077543566650298139, "heck", false, 12619621859430703986], [14455244907590647360, "tauri_codegen", false, 4800122882344689180], [17990358020177143287, "quote", false, 3342190915752399231]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-macros-7454175306a18472/dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}