{"rustc": 12610991425282158916, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 5347358027863023418, "path": 12467265937495298684, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 4762346219419302859], [3150220818285335163, "url", false, 6545121298977936358], [3191507132440681679, "serde_untagged", false, 2637891518968014335], [4071963112282141418, "serde_with", false, 12652789733424785793], [4899080583175475170, "semver", false, 8477718364738528688], [5986029879202738730, "log", false, 11012419202697596293], [6606131838865521726, "ctor", false, 9973762691008557868], [7170110829644101142, "json_patch", false, 2783240788583603145], [8319709847752024821, "uuid", false, 3706589622462861555], [9010263965687315507, "http", false, 13519363488636694896], [9451456094439810778, "regex", false, 1578284039987313505], [9556762810601084293, "brotli", false, 9965213716489469934], [9689903380558560274, "serde", false, 8746395647640681899], [10806645703491011684, "thiserror", false, 591031321967214246], [11989259058781683633, "dunce", false, 4762664516403991501], [13625485746686963219, "anyhow", false, 1360259769696615877], [15367738274754116744, "serde_json", false, 11945214587210310705], [15609422047640926750, "toml", false, 4301808769739516402], [15622660310229662834, "walkdir", false, 10227713011051960353], [15932120279885307830, "memchr", false, 12656516855636508274], [17146114186171651583, "infer", false, 6999444638843603427], [17155886227862585100, "glob", false, 6992076345437267531], [17186037756130803222, "phf", false, 566460904176372220]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-utils-3b64cbd49522a2de/dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}