{"rustc": 12610991425282158916, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 5347358027863023418, "path": 3909268098298153529, "deps": [[40386456601120721, "percent_encoding", false, 1137803582502636859], [1200537532907108615, "url<PERSON><PERSON>n", false, 4762346219419302859], [1386409696764982933, "objc2", false, 13381493469831469129], [2671782512663819132, "tauri_utils", false, 7377670638624321469], [3150220818285335163, "url", false, 6545121298977936358], [3331586631144870129, "getrandom", false, 12048417169512046364], [4143744114649553716, "raw_window_handle", false, 17963306475012769695], [4494683389616423722, "muda", false, 3402557886112551964], [4919829919303820331, "serialize_to_javascript", false, 10991870018545756452], [5986029879202738730, "log", false, 11012419202697596293], [6089812615193535349, "tauri_runtime", false, 1230350147409879120], [7573826311589115053, "tauri_macros", false, 7509837057625747030], [8589231650440095114, "embed_plist", false, 7831570007882775229], [9010263965687315507, "http", false, 13519363488636694896], [9689903380558560274, "serde", false, 8746395647640681899], [9859211262912517217, "objc2_foundation", false, 17239405890284809751], [10229185211513642314, "mime", false, 12148224291745353281], [10575598148575346675, "objc2_app_kit", false, 3478623338277614586], [10806645703491011684, "thiserror", false, 591031321967214246], [11599800339996261026, "tauri_runtime_wry", false, 5863413960412914006], [11989259058781683633, "dunce", false, 4762664516403991501], [12393800526703971956, "tokio", false, 9159312866441686205], [12565293087094287914, "window_vibrancy", false, 16004253691173756297], [12986574360607194341, "serde_repr", false, 15576495471913638004], [13077543566650298139, "heck", false, 12619621859430703986], [13625485746686963219, "anyhow", false, 1360259769696615877], [14039947826026167952, "build_script_build", false, 8570340215773675461], [14687315689416489882, "plist", false, 17885850194166087711], [15367738274754116744, "serde_json", false, 11945214587210310705], [16928111194414003569, "dirs", false, 10571653802019703233], [17155886227862585100, "glob", false, 6992076345437267531]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-6c06a1c3852ab6a9/dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}