{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 5347358027863023418, "path": 8831313169143023362, "deps": [[2671782512663819132, "tauri_utils", false, 7377670638624321469], [3150220818285335163, "url", false, 6545121298977936358], [4143744114649553716, "raw_window_handle", false, 17963306475012769695], [6089812615193535349, "build_script_build", false, 10259038736089491754], [7606335748176206944, "dpi", false, 9254586538295472835], [9010263965687315507, "http", false, 13519363488636694896], [9689903380558560274, "serde", false, 8746395647640681899], [10806645703491011684, "thiserror", false, 591031321967214246], [15367738274754116744, "serde_json", false, 11945214587210310705], [16727543399706004146, "cookie", false, 4239887056628112872]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-b0f4bba398b3006f/dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}