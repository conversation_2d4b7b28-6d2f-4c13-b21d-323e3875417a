{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"schemars_0_9\", \"schemars_1\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 3280471839685724059, "path": 14671899216621154753, "deps": [[7026957619838884710, "serde_with_macros", false, 18286525397173863384], [9689903380558560274, "serde", false, 8746395647640681899], [16257276029081467297, "serde_derive", false, 5982117137122149639]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/serde_with-0e924655ffaf1f74/dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}