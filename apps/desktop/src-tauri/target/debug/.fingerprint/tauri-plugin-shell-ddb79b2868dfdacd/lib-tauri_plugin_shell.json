{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 5347358027863023418, "path": 18222156294617963718, "deps": [[4972584477725338812, "build_script_build", false, 4676743463327714770], [5986029879202738730, "log", false, 11012419202697596293], [9451456094439810778, "regex", false, 1578284039987313505], [9689903380558560274, "serde", false, 8746395647640681899], [10806645703491011684, "thiserror", false, 591031321967214246], [11337703028400419576, "os_pipe", false, 5896170196968272711], [12393800526703971956, "tokio", false, 9159312866441686205], [14039947826026167952, "tauri", false, 7562284464424351719], [14564311161534545801, "encoding_rs", false, 13278195472815458994], [15367738274754116744, "serde_json", false, 11945214587210310705], [15722096100444777195, "shared_child", false, 16766467797217517386], [16192041687293812804, "open", false, 8966257089808556977]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-plugin-shell-ddb79b2868dfdacd/dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}