{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[\"win7-notifications\", \"windows-version\", \"windows7-compat\"]", "target": 11906320761866078153, "profile": 5347358027863023418, "path": 18046304825919249415, "deps": [[947818755262499932, "notify_rust", false, 336616911529917243], [3150220818285335163, "url", false, 6545121298977936358], [5986029879202738730, "log", false, 11012419202697596293], [7760050409050412348, "build_script_build", false, 4309848901134597499], [9689903380558560274, "serde", false, 8746395647640681899], [10806645703491011684, "thiserror", false, 591031321967214246], [12409575957772518135, "time", false, 11691515296079244360], [12986574360607194341, "serde_repr", false, 15576495471913638004], [13208667028893622512, "rand", false, 3668285497889790575], [14039947826026167952, "tauri", false, 7562284464424351719], [15367738274754116744, "serde_json", false, 11945214587210310705]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-plugin-notification-bd59fcdfcec3f0f3/dep-lib-tauri_plugin_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}