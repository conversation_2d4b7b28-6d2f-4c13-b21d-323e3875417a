{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 2040997289075261528, "path": 555184693129557424, "deps": [[1573238666360410412, "rand_chacha", false, 5133157510077942766], [4684437522915235464, "libc", false, 2377714323985590653], [18130209639506977569, "rand_core", false, 5190204032585124996]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rand-c85d250afe322256/dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}