{"rustc": 12610991425282158916, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2040997289075261528, "path": 12467265937495298684, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 847021728929869423], [3150220818285335163, "url", false, 1799102471434965605], [3191507132440681679, "serde_untagged", false, 10964690233003000920], [4071963112282141418, "serde_with", false, 11061628113252663635], [4899080583175475170, "semver", false, 8243413119968016728], [5986029879202738730, "log", false, 1209935733222880591], [6606131838865521726, "ctor", false, 10690331913246307087], [7170110829644101142, "json_patch", false, 11032702392044094425], [8319709847752024821, "uuid", false, 8307606384536223272], [9010263965687315507, "http", false, 3433976636652285146], [9451456094439810778, "regex", false, 13697533506946716704], [9556762810601084293, "brotli", false, 17920260332161866473], [9689903380558560274, "serde", false, 7454674820297192432], [10806645703491011684, "thiserror", false, 8980877348593830537], [11989259058781683633, "dunce", false, 16452121407500214398], [13625485746686963219, "anyhow", false, 16851264001863331394], [15367738274754116744, "serde_json", false, 8191116491794220986], [15609422047640926750, "toml", false, 18037716165905983917], [15622660310229662834, "walkdir", false, 12924735376632676665], [15932120279885307830, "memchr", false, 1406119373278843158], [17146114186171651583, "infer", false, 17174435892307837396], [17155886227862585100, "glob", false, 4801393906099144528], [17186037756130803222, "phf", false, 14080933969809495979]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-utils-cf68c77ab972c9db/dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}