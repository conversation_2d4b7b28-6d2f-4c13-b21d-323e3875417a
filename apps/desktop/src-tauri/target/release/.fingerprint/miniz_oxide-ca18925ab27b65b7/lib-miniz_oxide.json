{"rustc": 12610991425282158916, "features": "[\"default\", \"simd\", \"simd-adler32\", \"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 15743642140631288636, "path": 17354059993318206011, "deps": [[4018467389006652250, "simd_adler32", false, 11162726932124954328], [7911289239703230891, "adler2", false, 15951763086725936437]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/miniz_oxide-ca18925ab27b65b7/dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}