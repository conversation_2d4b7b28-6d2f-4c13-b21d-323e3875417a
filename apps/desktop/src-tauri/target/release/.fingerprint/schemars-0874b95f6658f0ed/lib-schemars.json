{"rustc": 12610991425282158916, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 1369601567987815722, "path": 9792031250658752118, "deps": [[3150220818285335163, "url", false, 262578665264449617], [6913375703034175521, "build_script_build", false, 1109396766996490817], [8319709847752024821, "uuid1", false, 16326766033539621146], [9122563107207267705, "dyn_clone", false, 11959631287867305691], [9689903380558560274, "serde", false, 9086625369266841935], [14923790796823607459, "indexmap", false, 14088923731318345834], [15367738274754116744, "serde_json", false, 16575440114180851632], [16071897500792579091, "schemars_derive", false, 10648084610396994251]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/schemars-0874b95f6658f0ed/dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}