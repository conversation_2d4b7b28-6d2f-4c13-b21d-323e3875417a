{"rustc": 12610991425282158916, "features": "[\"default\", \"simd\", \"simd-adler32\", \"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 5627820096486484124, "path": 17354059993318206011, "deps": [[4018467389006652250, "simd_adler32", false, 12408238436879324412], [7911289239703230891, "adler2", false, 2195525211301763117]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/miniz_oxide-c6149ea4f0d02c2b/dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}