{"rustc": 12610991425282158916, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2040997289075261528, "path": 16167103962419022307, "deps": [[555019317135488525, "regex_automata", false, 12668812914853857831], [2779309023524819297, "aho_corasick", false, 10948648462979306299], [9408802513701742484, "regex_syntax", false, 2594482753429962803], [15932120279885307830, "memchr", false, 1406119373278843158]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/regex-a26b2741ad471a02/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}