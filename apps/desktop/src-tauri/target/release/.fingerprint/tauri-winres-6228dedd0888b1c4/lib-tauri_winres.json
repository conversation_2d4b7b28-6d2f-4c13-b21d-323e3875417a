{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[]", "target": 2086567024422996381, "profile": 1369601567987815722, "path": 1592088255874697972, "deps": [[6493259146304816786, "indexmap", false, 81935225679765481], [6941104557053927479, "embed_resource", false, 6878345973186488307], [15609422047640926750, "toml", false, 5028479595842687112]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-winres-6228dedd0888b1c4/dep-lib-tauri_winres", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}