{"rustc": 12610991425282158916, "features": "[\"alloc-stdlib\", \"std\"]", "declared_features": "[\"alloc-stdlib\", \"benchmark\", \"billing\", \"default\", \"disable-timer\", \"disallow_large_window_size\", \"external-literal-probability\", \"ffi-api\", \"float64\", \"floating_point_context_mixing\", \"no-stdlib-ffi-binding\", \"pass-through-ffi-panics\", \"seccomp\", \"sha2\", \"simd\", \"std\", \"validation\", \"vector_scratch_space\"]", "target": 8433163163091947982, "profile": 2040997289075261528, "path": 3653309626074863965, "deps": [[9611597350722197978, "alloc_no_stdlib", false, 2839498614005450748], [16413620717702030930, "brotli_decompressor", false, 12266724121307062136], [17470296833448545982, "alloc_stdlib", false, 16721039665816813496]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/brotli-7ab64b7a0bc1c6e8/dep-lib-brotli", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}