{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"std\"]", "target": 15943748010645046320, "profile": 6243051256003197163, "path": 7893358888795266689, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/toml_write-85753fecc6e4e6c9/dep-lib-toml_write", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}