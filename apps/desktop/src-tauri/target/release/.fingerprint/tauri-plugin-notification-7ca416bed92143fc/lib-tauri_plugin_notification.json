{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[\"win7-notifications\", \"windows-version\", \"windows7-compat\"]", "target": 11906320761866078153, "profile": 2040997289075261528, "path": 18046304825919249415, "deps": [[947818755262499932, "notify_rust", false, 2613851038547336025], [3150220818285335163, "url", false, 1799102471434965605], [5986029879202738730, "log", false, 1209935733222880591], [7760050409050412348, "build_script_build", false, 3528156370842366502], [9689903380558560274, "serde", false, 7454674820297192432], [10806645703491011684, "thiserror", false, 8980877348593830537], [12409575957772518135, "time", false, 4326376949266534652], [12986574360607194341, "serde_repr", false, 3423861592810182235], [13208667028893622512, "rand", false, 5115700551752111243], [14039947826026167952, "tauri", false, 3915842513604625446], [15367738274754116744, "serde_json", false, 8191116491794220986]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-plugin-notification-7ca416bed92143fc/dep-lib-tauri_plugin_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}