{"rustc": 12610991425282158916, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2040997289075261528, "path": 3909268098298153529, "deps": [[40386456601120721, "percent_encoding", false, 16068023717710529484], [1200537532907108615, "url<PERSON><PERSON>n", false, 847021728929869423], [1386409696764982933, "objc2", false, 12541945517286428221], [2671782512663819132, "tauri_utils", false, 13806656371906183394], [3150220818285335163, "url", false, 1799102471434965605], [3331586631144870129, "getrandom", false, 15251282636238077477], [4143744114649553716, "raw_window_handle", false, 16802473498034310787], [4494683389616423722, "muda", false, 14915058600046117712], [4919829919303820331, "serialize_to_javascript", false, 10198695377495327274], [5986029879202738730, "log", false, 1209935733222880591], [6089812615193535349, "tauri_runtime", false, 17336657507194322716], [7573826311589115053, "tauri_macros", false, 5753911215138709710], [8589231650440095114, "embed_plist", false, 9133759348591715907], [9010263965687315507, "http", false, 3433976636652285146], [9689903380558560274, "serde", false, 7454674820297192432], [9859211262912517217, "objc2_foundation", false, 14870955589956545219], [10229185211513642314, "mime", false, 12004570514354335690], [10575598148575346675, "objc2_app_kit", false, 8578912706078158302], [10806645703491011684, "thiserror", false, 8980877348593830537], [11599800339996261026, "tauri_runtime_wry", false, 17429387084458467281], [11989259058781683633, "dunce", false, 16452121407500214398], [12393800526703971956, "tokio", false, 17057953735808033289], [12565293087094287914, "window_vibrancy", false, 15814519366302236439], [12986574360607194341, "serde_repr", false, 3423861592810182235], [13077543566650298139, "heck", false, 11501316444370062398], [13625485746686963219, "anyhow", false, 16851264001863331394], [14039947826026167952, "build_script_build", false, 15550708773370199349], [14687315689416489882, "plist", false, 10032610904474264832], [15367738274754116744, "serde_json", false, 8191116491794220986], [16928111194414003569, "dirs", false, 11701326187754595842], [17155886227862585100, "glob", false, 4801393906099144528]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-fd83af857aa75022/dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}