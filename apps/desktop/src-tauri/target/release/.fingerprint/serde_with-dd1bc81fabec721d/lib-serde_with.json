{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"schemars_0_9\", \"schemars_1\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 722405520834827680, "path": 14671899216621154753, "deps": [[7026957619838884710, "serde_with_macros", false, 126955215537243306], [9689903380558560274, "serde", false, 9086625369266841935], [16257276029081467297, "serde_derive", false, 12451787956100444594]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/serde_with-dd1bc81fabec721d/dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}