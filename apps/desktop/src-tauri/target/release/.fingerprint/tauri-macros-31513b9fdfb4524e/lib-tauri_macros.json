{"rustc": 12610991425282158916, "features": "[\"compression\", \"custom-protocol\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 1369601567987815722, "path": 6805413809821779974, "deps": [[2671782512663819132, "tauri_utils", false, 8882087436320492358], [3060637413840920116, "proc_macro2", false, 13310427550350149110], [4974441333307933176, "syn", false, 16965136849345849760], [13077543566650298139, "heck", false, 17329392710748965984], [14455244907590647360, "tauri_codegen", false, 15257188399955193624], [17990358020177143287, "quote", false, 15252477642945758894]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-macros-31513b9fdfb4524e/dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}