{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2040997289075261528, "path": 8831313169143023362, "deps": [[2671782512663819132, "tauri_utils", false, 13806656371906183394], [3150220818285335163, "url", false, 1799102471434965605], [4143744114649553716, "raw_window_handle", false, 16802473498034310787], [6089812615193535349, "build_script_build", false, 14846657760351402944], [7606335748176206944, "dpi", false, 17131087641220329252], [9010263965687315507, "http", false, 3433976636652285146], [9689903380558560274, "serde", false, 7454674820297192432], [10806645703491011684, "thiserror", false, 8980877348593830537], [15367738274754116744, "serde_json", false, 8191116491794220986], [16727543399706004146, "cookie", false, 18249863021477834327]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-runtime-dc0d5958b7f1cb8d/dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}