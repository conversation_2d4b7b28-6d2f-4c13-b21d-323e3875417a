{"rustc": 12610991425282158916, "features": "[\"CFArray\", \"CFBase\", \"CFCGTypes\", \"CFData\", \"CFDate\", \"CFDictionary\", \"CFRunLoop\", \"CFString\", \"CFURL\", \"CFUserNotification\", \"alloc\", \"bitflags\", \"objc2\", \"std\"]", "declared_features": "[\"CFArray\", \"CFAttributedString\", \"CFAvailability\", \"CFBag\", \"CFBase\", \"CFBinaryHeap\", \"CFBitVector\", \"CFBundle\", \"CFByteOrder\", \"CFCGTypes\", \"CFCalendar\", \"CFCharacterSet\", \"CFData\", \"CFDate\", \"CFDateFormatter\", \"CFDictionary\", \"CFError\", \"CFFileDescriptor\", \"CFFileSecurity\", \"CFLocale\", \"CFMachPort\", \"CFMessagePort\", \"CFNotificationCenter\", \"CFNumber\", \"CFNumberFormatter\", \"CFPlugIn\", \"CFPlugInCOM\", \"CFPreferences\", \"CFPropertyList\", \"CFRunLoop\", \"CFSet\", \"CFSocket\", \"CFStream\", \"CFString\", \"CFStringEncodingExt\", \"CFStringTokenizer\", \"CFTimeZone\", \"CFTree\", \"CFURL\", \"CFURLAccess\", \"CFURLEnumerator\", \"CFUUID\", \"CFUserNotification\", \"CFUtilities\", \"CFXMLNode\", \"CFXMLParser\", \"alloc\", \"bitflags\", \"block2\", \"default\", \"dispatch2\", \"libc\", \"objc2\", \"std\", \"unstable-coerce-pointee\"]", "target": 9250166696766853962, "profile": 10457427126514475017, "path": 1148141199167922500, "deps": [[1386409696764982933, "objc2", false, 12541945517286428221], [7896293946984509699, "bitflags", false, 16148346659777477705]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/objc2-core-foundation-53b42e3167731e38/dep-lib-objc2_core_foundation", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}