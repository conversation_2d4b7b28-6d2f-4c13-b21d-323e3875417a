{"rustc": 12610991425282158916, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 1369601567987815722, "path": 5426513424076302615, "deps": [[2671782512663819132, "tauri_utils", false, 8882087436320492358], [4899080583175475170, "semver", false, 6429604448980866365], [6913375703034175521, "schemars", false, 12818491244560675552], [7170110829644101142, "json_patch", false, 1264031241575613352], [9689903380558560274, "serde", false, 9086625369266841935], [12714016054753183456, "tauri_winres", false, 15517460278350261456], [13077543566650298139, "heck", false, 17329392710748965984], [13475171727366188400, "cargo_toml", false, 18229339701575701854], [13625485746686963219, "anyhow", false, 10492389938330198384], [15367738274754116744, "serde_json", false, 16575440114180851632], [15609422047640926750, "toml", false, 5028479595842687112], [15622660310229662834, "walkdir", false, 842132292916013280], [16928111194414003569, "dirs", false, 17536814211622115224], [17155886227862585100, "glob", false, 17799182141578130096]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-build-b5060e770d0adf7f/dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}