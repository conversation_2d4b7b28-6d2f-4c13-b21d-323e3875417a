{"rustc": 12610991425282158916, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 2040997289075261528, "path": 14739426990422067156, "deps": [[1386409696764982933, "objc2", false, 12541945517286428221], [2671782512663819132, "tauri_utils", false, 13806656371906183394], [3150220818285335163, "url", false, 1799102471434965605], [4143744114649553716, "raw_window_handle", false, 16802473498034310787], [5986029879202738730, "log", false, 1209935733222880591], [6089812615193535349, "tauri_runtime", false, 17336657507194322716], [8826339825490770380, "tao", false, 17050526836906528651], [9010263965687315507, "http", false, 3433976636652285146], [9141053277961803901, "wry", false, 5953476484133595707], [9859211262912517217, "objc2_foundation", false, 14870955589956545219], [10575598148575346675, "objc2_app_kit", false, 8578912706078158302], [11599800339996261026, "build_script_build", false, 7868717189349106886]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-runtime-wry-0ea9796777963b68/dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}