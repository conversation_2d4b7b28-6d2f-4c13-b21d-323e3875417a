{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 14724931235169721617, "path": 17506665195570010375, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/winnow-172dfc10da027c0c/dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}