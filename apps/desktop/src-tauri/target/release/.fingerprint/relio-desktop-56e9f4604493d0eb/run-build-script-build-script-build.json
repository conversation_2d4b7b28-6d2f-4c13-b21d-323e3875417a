{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[11821109851609088382, "build_script_build", false, 4817181455234185665], [14039947826026167952, "build_script_build", false, 15550708773370199349], [14525517306681678134, "build_script_build", false, 7636409020169280724], [6416823254013318197, "build_script_build", false, 1566499974233214133], [8324462083842905811, "build_script_build", false, 15454660352701266432], [7760050409050412348, "build_script_build", false, 3528156370842366502], [4972584477725338812, "build_script_build", false, 12239769427922666717], [724591372244576761, "build_script_build", false, 17722795367896672871]], "local": [{"RerunIfChanged": {"output": "release/build/relio-desktop-56e9f4604493d0eb/output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}