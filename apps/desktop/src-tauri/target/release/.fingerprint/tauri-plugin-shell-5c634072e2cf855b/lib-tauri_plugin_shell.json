{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 2040997289075261528, "path": 18222156294617963718, "deps": [[4972584477725338812, "build_script_build", false, 12239769427922666717], [5986029879202738730, "log", false, 1209935733222880591], [9451456094439810778, "regex", false, 13697533506946716704], [9689903380558560274, "serde", false, 7454674820297192432], [10806645703491011684, "thiserror", false, 8980877348593830537], [11337703028400419576, "os_pipe", false, 6903124163147520213], [12393800526703971956, "tokio", false, 17057953735808033289], [14039947826026167952, "tauri", false, 3915842513604625446], [14564311161534545801, "encoding_rs", false, 16327086189747138936], [15367738274754116744, "serde_json", false, 8191116491794220986], [15722096100444777195, "shared_child", false, 10084974010041805082], [16192041687293812804, "open", false, 13467740159542591121]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-plugin-shell-5c634072e2cf855b/dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}