const { db } = require('@repo/database/server');

async function debugJoinFields() {
  try {
    console.log('=== DEBUGGING JOIN FIELD TYPES ===');
    
    // Check a sample Property document 
    const property = await db.property.findFirst({
      where: { organizationId: '628bff8d44cd3e01b746b737' }
    });
    console.log('Property _id type:', typeof property?.id);
    console.log('Property _id value:', property?.id);
    
    // Check a sample PropertyLocation document
    const location = await db.propertyLocation.findFirst();
    console.log('\nPropertyLocation propertyId type:', typeof location?.propertyId);
    console.log('PropertyLocation propertyId value:', location?.propertyId);
    console.log('PropertyLocation _id value:', location?.id);
    
    // Test a direct Prisma relation query first
    console.log('\n=== TESTING PRISMA RELATION ===');
    const propertyWithLocation = await db.property.findFirst({
      where: { organizationId: '628bff8d44cd3e01b746b737' },
      include: { location: true }
    });
    console.log('Property with location found:', !!propertyWithLocation?.location);
    if (propertyWithLocation?.location) {
      console.log('Location city:', propertyWithLocation.location.address?.city);
    }
    
    // Test a simple aggregation to see what happens
    console.log('\n=== TESTING SIMPLE AGGREGATION ===');
    const testResult = await db.property.aggregateRaw({
      pipeline: [
        { $match: { organizationId: '628bff8d44cd3e01b746b737' } },
        { $limit: 1 },
        {
          $lookup: {
            from: "property_location",
            localField: "_id",
            foreignField: "propertyId",
            as: "location"
          }
        },
        { $project: { _id: 1, name: 1, "location._id": 1, "location.propertyId": 1, "location.address.city": 1 } }
      ]
    });
    console.log('Aggregation result:', JSON.stringify(testResult, null, 2));
    
    await db.$disconnect();
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

debugJoinFields(); 