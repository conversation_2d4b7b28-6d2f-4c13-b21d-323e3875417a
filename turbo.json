{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "globalEnv": ["*"], "tasks": {"build": {"dependsOn": ["^generate", "^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**", "out/**"]}, "type-check": {}, "clean": {"cache": false}, "generate": {"cache": false}, "dev": {"cache": false, "dependsOn": ["^generate"], "persistent": true}, "export": {"outputs": ["out/**"]}, "lint": {}, "start": {"cache": false, "dependsOn": ["^generate", "^build"], "persistent": true}, "android": {"cache": false, "dependsOn": ["^generate"], "persistent": true}, "ios": {"cache": false, "dependsOn": ["^generate"], "persistent": true}, "mobile": {"cache": false, "dependsOn": ["^generate"], "persistent": true}, "desktop": {"cache": false, "dependsOn": ["^generate", "^build"], "persistent": true}, "desktop:dev": {"cache": false, "dependsOn": ["^generate"], "persistent": true}}}